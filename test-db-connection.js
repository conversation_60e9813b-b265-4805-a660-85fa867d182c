const { pool } = require('./server/config/database');
const FacilityService = require('./server/services/facilityService');

async function testDatabaseConnection() {
    try {
        console.log('Testing database connection...');
        
        // Test basic connection
        const client = await pool.connect();
        console.log('✓ Database connection successful');
        client.release();
        
        // Test if tables exist
        const tablesQuery = `
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        `;
        
        const tablesResult = await pool.query(tablesQuery);
        console.log('✓ Found tables:', tablesResult.rows.map(row => row.table_name));
        
        // Test if venues exist
        const venuesQuery = 'SELECT name FROM venues WHERE is_active = true';
        const venuesResult = await pool.query(venuesQuery);
        console.log('✓ Found venues:', venuesResult.rows.map(row => row.name));
        
        // Test if Sirens facility exists
        try {
            const sirensVenue = await pool.query("SELECT * FROM venues WHERE name = 'Sirens FC Sports Complex'");
            if (sirensVenue.rows.length > 0) {
                console.log('✓ Sirens FC Sports Complex venue found');
                
                const sirensFacilities = await pool.query(`
                    SELECT f.name, f.sport_type, f.facility_type 
                    FROM facilities f 
                    JOIN venues v ON f.venue_id = v.id 
                    WHERE v.name = 'Sirens FC Sports Complex' AND f.is_active = true
                `);
                
                console.log('✓ Sirens facilities:', sirensFacilities.rows);
                
                // Test the service method
                const sirensMainFacility = await FacilityService.getSirensMainFacility();
                console.log('✓ Sirens main facility via service:', sirensMainFacility ? sirensMainFacility.name : 'Not found');
                
            } else {
                console.log('✗ Sirens FC Sports Complex venue not found');
            }
        } catch (error) {
            console.log('✗ Error checking Sirens facility:', error.message);
        }
        
    } catch (error) {
        console.error('✗ Database test failed:', error.message);
    } finally {
        await pool.end();
    }
}

testDatabaseConnection();