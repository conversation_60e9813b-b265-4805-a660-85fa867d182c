<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Account - Sports Malta</title>
    <meta name="description" content="Manage your Sports Malta account, view booking history, and update your preferences.">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/account.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="index.html">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="booking.html" class="nav-link">Book Now</a>
                        </li>
                        <li class="nav-item">
                            <a href="contact.html" class="nav-link">Contact</a>
                        </li>
                        <li class="nav-item">
                            <a href="account.html" class="nav-link active">Account</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
        <!-- Account Header -->
        <section class="account-header">
            <div class="container">
                <div class="account-header-content">
                    <div class="user-info">
                        <div class="user-avatar">
                            <span class="avatar-text">JD</span>
                        </div>
                        <div class="user-details">
                            <h1 class="user-name">John Doe</h1>
                            <p class="user-email"><EMAIL></p>
                            <div class="user-stats">
                                <div class="stat">
                                    <span class="stat-number">12</span>
                                    <span class="stat-label">Total Bookings</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-number">3</span>
                                    <span class="stat-label">Upcoming</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-number">⭐ 4.8</span>
                                    <span class="stat-label">Rating</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="quick-actions">
                        <a href="sirens-booking.html" class="btn btn-primary">New Booking</a>
                        <button class="btn btn-secondary" onclick="showEditProfile()">Edit Profile</button>
                        <button class="btn btn-outline" onclick="logout()">Logout</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Account Navigation -->
        <section class="account-nav">
            <div class="container">
                <nav class="account-tabs">
                    <button class="tab-btn active" onclick="showTab('dashboard')">Dashboard</button>
                    <button class="tab-btn" onclick="showTab('bookings')">My Bookings</button>
                    <button class="tab-btn" onclick="showTab('profile')">Profile</button>
                    <button class="tab-btn" onclick="showTab('preferences')">Preferences</button>
                    <button class="tab-btn" onclick="showTab('payment')">Payment Methods</button>
                </nav>
            </div>
        </section>

        <!-- Dashboard Tab -->
        <section id="dashboard-tab" class="tab-content active">
            <div class="container">
                <!-- Upcoming Bookings -->
                <div class="dashboard-section">
                    <h2 class="section-title">Upcoming Bookings</h2>
                    <div class="bookings-grid">
                        <div class="booking-card upcoming">
                            <div class="booking-header">
                                <div class="booking-sport">⚽ Football</div>
                                <div class="booking-status confirmed">Confirmed</div>
                            </div>
                            <div class="booking-details">
                                <h3 class="booking-venue">Sirens FC Sports Complex</h3>
                                <p class="booking-info">
                                    <span class="booking-date">Tomorrow, Jan 28</span>
                                    <span class="booking-time">7:00 PM - 8:00 PM</span>
                                </p>
                                <p class="booking-pitch">Pitch 1 - 5-a-side</p>
                            </div>
                            <div class="booking-actions">
                                <button class="btn btn-small btn-secondary" onclick="viewBooking('BK001')">View Details</button>
                                <button class="btn btn-small btn-outline" onclick="modifyBooking('BK001')">Modify</button>
                            </div>
                        </div>

                        <div class="booking-card upcoming">
                            <div class="booking-header">
                                <div class="booking-sport">🎾 Tennis</div>
                                <div class="booking-status confirmed">Confirmed</div>
                            </div>
                            <div class="booking-details">
                                <h3 class="booking-venue">Malta Tennis Academy</h3>
                                <p class="booking-info">
                                    <span class="booking-date">Jan 30, 2024</span>
                                    <span class="booking-time">6:00 PM - 7:00 PM</span>
                                </p>
                                <p class="booking-pitch">Hard Court 2</p>
                            </div>
                            <div class="booking-actions">
                                <button class="btn btn-small btn-secondary" onclick="viewBooking('BK002')">View Details</button>
                                <button class="btn btn-small btn-outline" onclick="modifyBooking('BK002')">Modify</button>
                            </div>
                        </div>

                        <div class="booking-card upcoming">
                            <div class="booking-header">
                                <div class="booking-sport">🏓 Padel</div>
                                <div class="booking-status pending">Pending Payment</div>
                            </div>
                            <div class="booking-details">
                                <h3 class="booking-venue">Valletta Padel Club</h3>
                                <p class="booking-info">
                                    <span class="booking-date">Feb 2, 2024</span>
                                    <span class="booking-time">8:00 PM - 9:00 PM</span>
                                </p>
                                <p class="booking-pitch">Court 1 - Premium</p>
                            </div>
                            <div class="booking-actions">
                                <button class="btn btn-small btn-primary" onclick="payBooking('BK003')">Pay Now</button>
                                <button class="btn btn-small btn-outline" onclick="cancelBooking('BK003')">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="dashboard-section">
                    <h2 class="section-title">Your Activity</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📅</div>
                            <div class="stat-content">
                                <div class="stat-number">12</div>
                                <div class="stat-label">Total Bookings</div>
                                <div class="stat-change">+3 this month</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">⚽</div>
                            <div class="stat-content">
                                <div class="stat-number">8</div>
                                <div class="stat-label">Football Sessions</div>
                                <div class="stat-change">Most played sport</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-content">
                                <div class="stat-number">€340</div>
                                <div class="stat-label">Total Spent</div>
                                <div class="stat-change">€85 this month</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon">🏆</div>
                            <div class="stat-content">
                                <div class="stat-number">4.8</div>
                                <div class="stat-label">Average Rating</div>
                                <div class="stat-change">Excellent player</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Favorite Venues -->
                <div class="dashboard-section">
                    <h2 class="section-title">Your Favorite Venues</h2>
                    <div class="venues-grid">
                        <div class="venue-card-small">
                            <div class="venue-image">
                                <img src="images/sirens-fc-small.jpg" alt="Sirens FC" loading="lazy">
                            </div>
                            <div class="venue-info">
                                <h3 class="venue-name">Sirens FC</h3>
                                <p class="venue-location">St. Pauls Bay</p>
                                <div class="venue-stats">
                                    <span class="booking-count">6 bookings</span>
                                    <span class="venue-rating">⭐ 4.9</span>
                                </div>
                            </div>
                            <a href="sirens-booking.html" class="btn btn-small btn-primary">Book Again</a>
                        </div>

                        <div class="venue-card-small">
                            <div class="venue-image">
                                <img src="images/tennis-academy-small.jpg" alt="Tennis Academy" loading="lazy">
                            </div>
                            <div class="venue-info">
                                <h3 class="venue-name">Tennis Academy</h3>
                                <p class="venue-location">Sliema</p>
                                <div class="venue-stats">
                                    <span class="booking-count">3 bookings</span>
                                    <span class="venue-rating">⭐ 4.8</span>
                                </div>
                            </div>
                            <a href="tennis-academy-booking.html" class="btn btn-small btn-primary">Book Again</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bookings Tab -->
        <section id="bookings-tab" class="tab-content">
            <div class="container">
                <div class="bookings-header">
                    <h2 class="section-title">My Bookings</h2>
                    <div class="bookings-filters">
                        <select id="booking-filter" onchange="filterBookings()">
                            <option value="all">All Bookings</option>
                            <option value="upcoming">Upcoming</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        <select id="sport-filter" onchange="filterBookings()">
                            <option value="all">All Sports</option>
                            <option value="football">Football</option>
                            <option value="tennis">Tennis</option>
                            <option value="padel">Padel</option>
                        </select>
                    </div>
                </div>

                <div class="bookings-list">
                    <!-- Upcoming Bookings -->
                    <div class="booking-item upcoming" data-sport="football">
                        <div class="booking-date-badge">
                            <div class="date-day">28</div>
                            <div class="date-month">Jan</div>
                        </div>
                        <div class="booking-content">
                            <div class="booking-main">
                                <h3 class="booking-title">⚽ Football - Sirens FC Sports Complex</h3>
                                <p class="booking-details-text">Pitch 1 - 5-a-side | 7:00 PM - 8:00 PM</p>
                                <div class="booking-meta">
                                    <span class="booking-ref">Ref: BK001</span>
                                    <span class="booking-price">€25.00</span>
                                </div>
                            </div>
                            <div class="booking-status-badge confirmed">Confirmed</div>
                        </div>
                        <div class="booking-actions-list">
                            <button class="btn btn-small btn-secondary" onclick="viewBooking('BK001')">View</button>
                            <button class="btn btn-small btn-outline" onclick="modifyBooking('BK001')">Modify</button>
                            <button class="btn btn-small btn-danger" onclick="cancelBooking('BK001')">Cancel</button>
                        </div>
                    </div>

                    <!-- More booking items would be loaded here -->
                    <div class="booking-item completed" data-sport="tennis">
                        <div class="booking-date-badge">
                            <div class="date-day">25</div>
                            <div class="date-month">Jan</div>
                        </div>
                        <div class="booking-content">
                            <div class="booking-main">
                                <h3 class="booking-title">🎾 Tennis - Malta Tennis Academy</h3>
                                <p class="booking-details-text">Hard Court 1 | 6:00 PM - 7:00 PM</p>
                                <div class="booking-meta">
                                    <span class="booking-ref">Ref: BK000</span>
                                    <span class="booking-price">€30.00</span>
                                </div>
                            </div>
                            <div class="booking-status-badge completed">Completed</div>
                        </div>
                        <div class="booking-actions-list">
                            <button class="btn btn-small btn-secondary" onclick="viewBooking('BK000')">View</button>
                            <button class="btn btn-small btn-primary" onclick="rebookSession('BK000')">Book Again</button>
                            <button class="btn btn-small btn-outline" onclick="rateBooking('BK000')">Rate</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Profile Tab -->
        <section id="profile-tab" class="tab-content">
            <div class="container">
                <div class="profile-section">
                    <h2 class="section-title">Profile Information</h2>
                    <form class="profile-form" id="profile-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="first-name">First Name</label>
                                <input type="text" id="first-name" name="firstName" value="John" required>
                            </div>
                            <div class="form-group">
                                <label for="last-name">Last Name</label>
                                <input type="text" id="last-name" name="lastName" value="Doe" required>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" value="<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" value="+356 7912 3456">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="date-of-birth">Date of Birth</label>
                            <input type="date" id="date-of-birth" name="dateOfBirth" value="1990-05-15">
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Profile</button>
                        </div>
                    </form>
                </div>

                <div class="profile-section">
                    <h2 class="section-title">Change Password</h2>
                    <form class="password-form" id="password-form">
                        <div class="form-group">
                            <label for="current-password">Current Password</label>
                            <input type="password" id="current-password" name="currentPassword" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="new-password">New Password</label>
                            <input type="password" id="new-password" name="newPassword" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm-password">Confirm New Password</label>
                            <input type="password" id="confirm-password" name="confirmPassword" required>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-secondary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Preferences Tab -->
        <section id="preferences-tab" class="tab-content">
            <div class="container">
                <div class="preferences-section">
                    <h2 class="section-title">Notification Preferences</h2>
                    <form class="preferences-form" id="preferences-form">
                        <div class="preference-group">
                            <h3>Email Notifications</h3>
                            <div class="preference-item">
                                <label class="switch">
                                    <input type="checkbox" name="emailBookingConfirmation" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="preference-info">
                                    <h4>Booking Confirmations</h4>
                                    <p>Receive email confirmations for new bookings</p>
                                </div>
                            </div>
                            
                            <div class="preference-item">
                                <label class="switch">
                                    <input type="checkbox" name="emailReminders" checked>
                                    <span class="slider"></span>
                                </label>
                                <div class="preference-info">
                                    <h4>Booking Reminders</h4>
                                    <p>Get reminded about upcoming bookings</p>
                                </div>
                            </div>
                            
                            <div class="preference-item">
                                <label class="switch">
                                    <input type="checkbox" name="emailNewsletter">
                                    <span class="slider"></span>
                                </label>
                                <div class="preference-info">
                                    <h4>Newsletter</h4>
                                    <p>Receive updates and special offers</p>
                                </div>
                            </div>
                        </div>

                        <div class="preference-group">
                            <h3>Favorite Sports</h3>
                            <div class="sports-preferences">
                                <label class="sport-checkbox">
                                    <input type="checkbox" name="favoriteFootball" checked>
                                    <span class="sport-icon">⚽</span>
                                    <span class="sport-name">Football</span>
                                </label>
                                
                                <label class="sport-checkbox">
                                    <input type="checkbox" name="favoriteTennis" checked>
                                    <span class="sport-icon">🎾</span>
                                    <span class="sport-name">Tennis</span>
                                </label>
                                
                                <label class="sport-checkbox">
                                    <input type="checkbox" name="favoritePadel">
                                    <span class="sport-icon">🏓</span>
                                    <span class="sport-name">Padel</span>
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Save Preferences</button>
                        </div>
                    </form>
                </div>
            </div>
        </section>

        <!-- Payment Methods Tab -->
        <section id="payment-tab" class="tab-content">
            <div class="container">
                <div class="payment-section">
                    <h2 class="section-title">Payment Methods</h2>
                    <div class="payment-methods">
                        <div class="payment-card">
                            <div class="card-info">
                                <div class="card-icon">💳</div>
                                <div class="card-details">
                                    <h3>Visa ending in 1234</h3>
                                    <p>Expires 12/25</p>
                                </div>
                            </div>
                            <div class="card-actions">
                                <button class="btn btn-small btn-outline">Edit</button>
                                <button class="btn btn-small btn-danger">Remove</button>
                            </div>
                        </div>
                        
                        <div class="add-payment">
                            <button class="btn btn-secondary" onclick="addPaymentMethod()">
                                <span>+</span> Add New Payment Method
                            </button>
                        </div>
                    </div>
                </div>

                <div class="payment-section">
                    <h2 class="section-title">Billing History</h2>
                    <div class="billing-history">
                        <div class="billing-item">
                            <div class="billing-date">Jan 25, 2024</div>
                            <div class="billing-description">Tennis Court Booking - Malta Tennis Academy</div>
                            <div class="billing-amount">€30.00</div>
                            <div class="billing-status paid">Paid</div>
                            <button class="btn btn-small btn-outline">Download</button>
                        </div>
                        
                        <div class="billing-item">
                            <div class="billing-date">Jan 20, 2024</div>
                            <div class="billing-description">Football Pitch Booking - Sirens FC</div>
                            <div class="billing-amount">€25.00</div>
                            <div class="billing-status paid">Paid</div>
                            <button class="btn btn-small btn-outline">Download</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <span class="logo-text">SM</span>
                        <span class="logo-subtitle">Sports Malta</span>
                    </div>
                    <p class="footer-description">Your premier destination for booking sports facilities across Malta.</p>
                    <div class="social-links">
                        <a href="#" class="social-link" aria-label="Instagram">📷</a>
                        <a href="#" class="social-link" aria-label="Facebook">📘</a>
                    </div>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Quick Links</h3>
                    <ul class="footer-links">
                        <li><a href="booking.html">Book Now</a></li>
                        <li><a href="contact.html">Contact Us</a></li>
                        <li><a href="account.html">My Account</a></li>
                        <li><a href="index.html#sports">Sports</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Sports</h3>
                    <ul class="footer-links">
                        <li><a href="football.html">Football</a></li>
                        <li><a href="tennis.html">Tennis</a></li>
                        <li><a href="padel.html">Padel</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h3 class="footer-title">Contact</h3>
                    <div class="contact-info">
                        <p>📧 <EMAIL></p>
                        <p>📞 +356 1234 5678</p>
                        <p>📍 Malta</p>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Sports Malta. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Authentication Check Script -->
    <script>
        // Check authentication before loading the page
        document.addEventListener('DOMContentLoaded', async function() {
            // Load auth handler first
            if (!window.authHandler) {
                // If auth handler not loaded, redirect to login
                window.location.href = '/login.html?return=' + encodeURIComponent(window.location.pathname);
                return;
            }
            
            const authStatus = await window.authHandler.checkAuthStatus();
            
            if (!authStatus.success) {
                // User is not authenticated, redirect to login
                window.authHandler.redirectToLogin('/account.html');
                return;
            }
            
            // User is authenticated, continue loading the page
            console.log('User authenticated:', authStatus.user);
        });
    </script>
    
    <script src="js/auth.js"></script>
    <script src="js/main.js"></script>
    <script src="js/account.js"></script>
</body>
</html>