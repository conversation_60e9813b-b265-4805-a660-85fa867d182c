/**
 * Account Page Handler
 * Manages user account functionality and authentication
 */
class AccountManager {
    constructor() {
        this.currentUser = null;
        this.currentTab = 'dashboard';
        this.init();
    }

    async init() {
        // Check authentication status
        await this.checkAuthentication();
        
        // Initialize event listeners
        this.initEventListeners();
        
        // Load user data if authenticated
        if (this.currentUser) {
            this.loadUserData();
        }
    }

    async checkAuthentication() {
        const authStatus = await window.authHandler.checkAuthStatus();
        
        if (!authStatus.success) {
            // User is not authenticated, redirect to login
            window.authHandler.redirectToLogin('/account.html');
            return;
        }
        
        this.currentUser = authStatus.user;
    }

    loadUserData() {
        if (!this.currentUser) return;
        
        // Update user info in header
        this.updateUserHeader();
        
        // Load user's bookings
        this.loadUserBookings();
        
        // Update profile form
        this.updateProfileForm();
    }

    updateUserHeader() {
        const userNameEl = document.querySelector('.user-name');
        const userEmailEl = document.querySelector('.user-email');
        const avatarTextEl = document.querySelector('.avatar-text');
        
        if (userNameEl) {
            userNameEl.textContent = `${this.currentUser.firstName} ${this.currentUser.lastName}`;
        }
        
        if (userEmailEl) {
            userEmailEl.textContent = this.currentUser.email;
        }
        
        if (avatarTextEl) {
            const initials = `${this.currentUser.firstName.charAt(0)}${this.currentUser.lastName.charAt(0)}`;
            avatarTextEl.textContent = initials.toUpperCase();
        }
    }

    async loadUserBookings() {
        try {
            const response = await window.authHandler.makeAuthenticatedRequest('/api/bookings/user');
            
            if (response && response.ok) {
                const data = await response.json();
                
                if (data.success) {
                    this.displayBookings(data.bookings);
                    this.updateBookingStats(data.bookings);
                }
            }
        } catch (error) {
            console.error('Error loading bookings:', error);
        }
    }

    displayBookings(bookings) {
        const upcomingBookings = bookings.filter(booking => 
            new Date(booking.booking_date) >= new Date() && booking.status !== 'cancelled'
        );
        
        const completedBookings = bookings.filter(booking => 
            new Date(booking.booking_date) < new Date() && booking.status === 'completed'
        );
        
        // Update dashboard upcoming bookings
        this.updateDashboardBookings(upcomingBookings.slice(0, 3));
        
        // Update bookings tab
        this.updateBookingsTab(bookings);
    }

    updateDashboardBookings(upcomingBookings) {
        const bookingsGrid = document.querySelector('.bookings-grid');
        if (!bookingsGrid) return;
        
        if (upcomingBookings.length === 0) {
            bookingsGrid.innerHTML = `
                <div class="no-bookings">
                    <p>No upcoming bookings</p>
                    <a href="sirens-booking.html" class="btn btn-primary">Make a Booking</a>
                </div>
            `;
            return;
        }
        
        bookingsGrid.innerHTML = upcomingBookings.map(booking => `
            <div class="booking-card upcoming">
                <div class="booking-header">
                    <div class="booking-sport">${this.getSportIcon(booking.sport)} ${booking.sport}</div>
                    <div class="booking-status ${booking.status}">${booking.status}</div>
                </div>
                <div class="booking-details">
                    <h3 class="booking-venue">${booking.venue_name}</h3>
                    <p class="booking-info">
                        <span class="booking-date">${this.formatDate(booking.booking_date)}</span>
                        <span class="booking-time">${booking.start_time} - ${booking.end_time}</span>
                    </p>
                    <p class="booking-pitch">${booking.facility_name}</p>
                </div>
                <div class="booking-actions">
                    <button class="btn btn-small btn-secondary" onclick="accountManager.viewBooking('${booking.id}')">View Details</button>
                    <button class="btn btn-small btn-outline" onclick="accountManager.modifyBooking('${booking.id}')">Modify</button>
                </div>
            </div>
        `).join('');
    }

    updateBookingsTab(bookings) {
        const bookingsList = document.querySelector('.bookings-list');
        if (!bookingsList) return;
        
        if (bookings.length === 0) {
            bookingsList.innerHTML = `
                <div class="no-bookings">
                    <p>No bookings found</p>
                    <a href="sirens-booking.html" class="btn btn-primary">Make Your First Booking</a>
                </div>
            `;
            return;
        }
        
        bookingsList.innerHTML = bookings.map(booking => `
            <div class="booking-item ${booking.status}" data-sport="${booking.sport.toLowerCase()}">
                <div class="booking-date-badge">
                    <div class="date-day">${new Date(booking.booking_date).getDate()}</div>
                    <div class="date-month">${new Date(booking.booking_date).toLocaleDateString('en', { month: 'short' })}</div>
                </div>
                <div class="booking-content">
                    <div class="booking-main">
                        <h3 class="booking-title">${this.getSportIcon(booking.sport)} ${booking.sport} - ${booking.venue_name}</h3>
                        <p class="booking-details-text">${booking.facility_name} | ${booking.start_time} - ${booking.end_time}</p>
                        <div class="booking-meta">
                            <span class="booking-ref">Ref: ${booking.booking_reference}</span>
                            <span class="booking-price">€${booking.total_price}</span>
                        </div>
                    </div>
                    <div class="booking-status-badge ${booking.status}">${booking.status}</div>
                </div>
                <div class="booking-actions-list">
                    <button class="btn btn-small btn-secondary" onclick="accountManager.viewBooking('${booking.id}')">View</button>
                    ${booking.status === 'upcoming' ? 
                        `<button class="btn btn-small btn-outline" onclick="accountManager.modifyBooking('${booking.id}')">Modify</button>
                         <button class="btn btn-small btn-danger" onclick="accountManager.cancelBooking('${booking.id}')">Cancel</button>` :
                        `<button class="btn btn-small btn-primary" onclick="accountManager.rebookSession('${booking.id}')">Book Again</button>`
                    }
                </div>
            </div>
        `).join('');
    }

    updateBookingStats(bookings) {
        const totalBookings = bookings.length;
        const upcomingBookings = bookings.filter(booking => 
            new Date(booking.booking_date) >= new Date() && booking.status !== 'cancelled'
        ).length;
        
        const totalSpent = bookings.reduce((sum, booking) => sum + parseFloat(booking.total_price || 0), 0);
        
        // Update stats in dashboard
        const statNumbers = document.querySelectorAll('.stat-number');
        if (statNumbers.length >= 3) {
            statNumbers[0].textContent = totalBookings;
            statNumbers[2].textContent = `€${totalSpent.toFixed(0)}`;
        }
        
        // Update user stats in header
        const userStats = document.querySelector('.user-stats');
        if (userStats) {
            userStats.innerHTML = `
                <div class="stat">
                    <span class="stat-number">${totalBookings}</span>
                    <span class="stat-label">Total Bookings</span>
                </div>
                <div class="stat">
                    <span class="stat-number">${upcomingBookings}</span>
                    <span class="stat-label">Upcoming</span>
                </div>
                <div class="stat">
                    <span class="stat-number">⭐ 4.8</span>
                    <span class="stat-label">Rating</span>
                </div>
            `;
        }
    }

    updateProfileForm() {
        const firstNameInput = document.getElementById('first-name');
        const lastNameInput = document.getElementById('last-name');
        const emailInput = document.getElementById('email');
        const phoneInput = document.getElementById('phone');
        
        if (firstNameInput) firstNameInput.value = this.currentUser.firstName || '';
        if (lastNameInput) lastNameInput.value = this.currentUser.lastName || '';
        if (emailInput) emailInput.value = this.currentUser.email || '';
        if (phoneInput) phoneInput.value = this.currentUser.phone || '';
    }

    initEventListeners() {
        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.getAttribute('onclick').match(/'([^']+)'/)[1];
                this.showTab(tabName);
            });
        });
        
        // Profile form submission
        const profileForm = document.getElementById('profile-form');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => this.handleProfileUpdate(e));
        }
        
        // Password form submission
        const passwordForm = document.getElementById('password-form');
        if (passwordForm) {
            passwordForm.addEventListener('submit', (e) => this.handlePasswordChange(e));
        }
        
        // Preferences form submission
        const preferencesForm = document.getElementById('preferences-form');
        if (preferencesForm) {
            preferencesForm.addEventListener('submit', (e) => this.handlePreferencesUpdate(e));
        }
    }

    showTab(tabName) {
        // Hide all tabs
        document.querySelectorAll('.tab-content').forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // Show selected tab
        const selectedTab = document.getElementById(`${tabName}-tab`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }
        
        // Add active class to selected button
        const selectedBtn = document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedBtn) {
            selectedBtn.classList.add('active');
        }
        
        this.currentTab = tabName;
    }

    async handleProfileUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const updateData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone')
        };
        
        try {
            const response = await window.authHandler.makeAuthenticatedRequest('/api/auth/profile', {
                method: 'PUT',
                body: JSON.stringify(updateData)
            });
            
            if (response && response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.currentUser = { ...this.currentUser, ...updateData };
                    this.updateUserHeader();
                    this.showMessage('Profile updated successfully!', 'success');
                }
            }
        } catch (error) {
            console.error('Profile update error:', error);
            this.showMessage('Failed to update profile', 'error');
        }
    }

    async handlePasswordChange(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const currentPassword = formData.get('currentPassword');
        const newPassword = formData.get('newPassword');
        const confirmPassword = formData.get('confirmPassword');
        
        if (newPassword !== confirmPassword) {
            this.showMessage('New passwords do not match', 'error');
            return;
        }
        
        try {
            const response = await window.authHandler.makeAuthenticatedRequest('/api/auth/change-password', {
                method: 'POST',
                body: JSON.stringify({ currentPassword, newPassword })
            });
            
            if (response && response.ok) {
                const data = await response.json();
                if (data.success) {
                    e.target.reset();
                    this.showMessage('Password changed successfully!', 'success');
                }
            }
        } catch (error) {
            console.error('Password change error:', error);
            this.showMessage('Failed to change password', 'error');
        }
    }

    async handlePreferencesUpdate(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const preferences = {
            emailBookingConfirmation: formData.has('emailBookingConfirmation'),
            emailReminders: formData.has('emailReminders'),
            emailNewsletter: formData.has('emailNewsletter'),
            favoriteFootball: formData.has('favoriteFootball'),
            favoriteTennis: formData.has('favoriteTennis'),
            favoritePadel: formData.has('favoritePadel')
        };
        
        try {
            const response = await window.authHandler.makeAuthenticatedRequest('/api/auth/preferences', {
                method: 'PUT',
                body: JSON.stringify(preferences)
            });
            
            if (response && response.ok) {
                this.showMessage('Preferences updated successfully!', 'success');
            }
        } catch (error) {
            console.error('Preferences update error:', error);
            this.showMessage('Failed to update preferences', 'error');
        }
    }

    async logout() {
        await window.authHandler.logout();
        window.location.href = '/index.html';
    }

    // Booking action methods
    viewBooking(bookingId) {
        // Implementation for viewing booking details
        console.log('View booking:', bookingId);
    }

    modifyBooking(bookingId) {
        // Implementation for modifying booking
        console.log('Modify booking:', bookingId);
    }

    cancelBooking(bookingId) {
        // Implementation for canceling booking
        console.log('Cancel booking:', bookingId);
    }

    rebookSession(bookingId) {
        // Implementation for rebooking
        console.log('Rebook session:', bookingId);
    }

    // Utility methods
    getSportIcon(sport) {
        const icons = {
            'Football': '⚽',
            'Tennis': '🎾',
            'Padel': '🏓'
        };
        return icons[sport] || '🏃';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        if (date.toDateString() === today.toDateString()) {
            return 'Today';
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow';
        } else {
            return date.toLocaleDateString('en', { 
                month: 'short', 
                day: 'numeric',
                year: date.getFullYear() !== today.getFullYear() ? 'numeric' : undefined
            });
        }
    }

    showMessage(message, type = 'info') {
        // Create and show a temporary message
        const messageEl = document.createElement('div');
        messageEl.className = `message ${type}`;
        messageEl.textContent = message;
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'success' ? '#c6f6d5' : type === 'error' ? '#fed7d7' : '#bee3f8'};
            color: ${type === 'success' ? '#38a169' : type === 'error' ? '#e53e3e' : '#3182ce'};
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;
        
        document.body.appendChild(messageEl);
        
        setTimeout(() => {
            messageEl.remove();
        }, 5000);
    }
}

// Global functions for onclick handlers
function showTab(tabName) {
    if (window.accountManager) {
        window.accountManager.showTab(tabName);
    }
}

function logout() {
    if (window.accountManager) {
        window.accountManager.logout();
    }
}

function showEditProfile() {
    showTab('profile');
}

// Initialize account manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.accountManager = new AccountManager();
});