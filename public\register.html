<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Sports Malta</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .register-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 3rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .register-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .register-header h2 {
            color: #2d3748;
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .register-header p {
            color: #718096;
            font-size: 1rem;
        }
        
        .register-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .form-row {
            display: flex;
            gap: 1rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            flex: 1;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2d3748;
            font-size: 0.9rem;
        }
        
        .form-group input {
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f7fafc;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        
        .form-group small {
            margin-top: 0.25rem;
            color: #718096;
            font-size: 0.8rem;
        }
        
        .btn-register {
            padding: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            margin-top: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-register:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .error-message {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            padding: 0.75rem;
            background: #fed7d7;
            border-radius: 6px;
            border-left: 4px solid #e53e3e;
        }
        
        .success-message {
            color: #38a169;
            font-size: 0.875rem;
            margin-top: 0.5rem;
            padding: 0.75rem;
            background: #c6f6d5;
            border-radius: 6px;
            border-left: 4px solid #38a169;
        }
        
        .field-error {
            border-color: #e53e3e !important;
            background: #fed7d7 !important;
        }
        
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }
        
        .login-link {
            text-align: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e2e8f0;
        }
        
        .login-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }
        
        .login-link a:hover {
            color: #764ba2;
        }
        
        @media (max-width: 600px) {
            .register-container {
                margin: 20px;
                padding: 2rem;
            }
            
            .form-row {
                flex-direction: column;
                gap: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <h2>Create Your Account</h2>
            <p>Join Sports Malta to book your favorite sports facilities</p>
        </div>
        
        <form class="register-form" id="registerForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="firstName">First Name *</label>
                    <input type="text" id="firstName" name="firstName" required>
                </div>
                
                <div class="form-group">
                    <label for="lastName">Last Name *</label>
                    <input type="text" id="lastName" name="lastName" required>
                </div>
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required>
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number (Optional)</label>
                <input type="tel" id="phone" name="phone" placeholder="+356 1234 5678">
            </div>
            
            <div class="form-group">
                <label for="password">Password *</label>
                <input type="password" id="password" name="password" required minlength="6">
                <small style="color: #6c757d; font-size: 0.8rem;">Minimum 6 characters</small>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password *</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            
            <button type="submit" class="btn-register" id="registerBtn">
                Create Account
            </button>
            
            <div id="message"></div>
        </form>
        
        <div class="login-link">
            <p>Already have an account? <a href="login.html">Login here</a></p>
        </div>
    </div>

    <script src="js/auth.js"></script>
    <script>
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const form = e.target;
            const formData = new FormData(form);
            const messageDiv = document.getElementById('message');
            const registerBtn = document.getElementById('registerBtn');
            
            // Clear previous errors
            document.querySelectorAll('.field-error').forEach(field => {
                field.classList.remove('field-error');
            });
            
            // Get form values
            const firstName = formData.get('firstName').trim();
            const lastName = formData.get('lastName').trim();
            const email = formData.get('email').trim();
            const phone = formData.get('phone').trim();
            const password = formData.get('password');
            const confirmPassword = formData.get('confirmPassword');
            
            // Client-side validation
            let hasErrors = false;
            
            if (!firstName) {
                document.getElementById('firstName').classList.add('field-error');
                hasErrors = true;
            }
            
            if (!lastName) {
                document.getElementById('lastName').classList.add('field-error');
                hasErrors = true;
            }
            
            if (!email || !email.includes('@')) {
                document.getElementById('email').classList.add('field-error');
                hasErrors = true;
            }
            
            if (!password || password.length < 6) {
                document.getElementById('password').classList.add('field-error');
                hasErrors = true;
            }
            
            if (password !== confirmPassword) {
                document.getElementById('confirmPassword').classList.add('field-error');
                messageDiv.innerHTML = '<div class="error-message">Passwords do not match</div>';
                hasErrors = true;
            }
            
            if (hasErrors) {
                if (!messageDiv.innerHTML) {
                    messageDiv.innerHTML = '<div class="error-message">Please fill in all required fields correctly</div>';
                }
                return;
            }
            
            // Show loading state
            registerBtn.disabled = true;
            registerBtn.textContent = 'Creating Account...';
            form.classList.add('loading');
            
            try {
                const result = await window.authHandler.register({
                    firstName,
                    lastName,
                    email,
                    phone: phone || undefined,
                    password
                });
                
                if (result.success) {
                    messageDiv.innerHTML = '<div class="success-message">Account created successfully! Redirecting to login...</div>';
                    
                    // Clear the form
                    form.reset();
                    
                    setTimeout(() => {
                        window.location.href = 'login.html?message=registration-success';
                    }, 2000);
                } else {
                    messageDiv.innerHTML = `<div class="error-message">${result.error.message || 'Registration failed'}</div>`;
                    
                    // Highlight fields with errors if available
                    if (result.error.details) {
                        result.error.details.forEach(detail => {
                            const field = document.getElementById(detail.param);
                            if (field) {
                                field.classList.add('field-error');
                            }
                        });
                    }
                }
            } catch (error) {
                console.error('Registration error:', error);
                messageDiv.innerHTML = '<div class="error-message">An error occurred. Please try again.</div>';
            } finally {
                // Reset loading state
                registerBtn.disabled = false;
                registerBtn.textContent = 'Create Account';
                form.classList.remove('loading');
            }
        });
        
        // Real-time password confirmation validation
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.classList.add('field-error');
            } else {
                this.classList.remove('field-error');
            }
        });
    </script>
</body>
</html>