const express = require('express');
const BookingController = require('../controllers/bookingController');
const { verifyToken, requireAuth } = require('../middleware/auth');

const router = express.Router();

/**
 * GET /api/bookings/availability/:facilityId/:date
 * Get availability for a specific facility and date
 */
router.get(
    '/availability/:facilityId/:date',
    BookingController.validateAvailabilityParams(),
    BookingController.getAvailability
);

/**
 * GET /api/bookings/availability/sirens/:date
 * Get availability for Sirens FC main facility
 */
router.get(
    '/availability/sirens/:date',
    BookingController.getSirensAvailability
);

/**
 * POST /api/bookings/check-availability
 * Check availability for specific time slot and pitch configuration
 */
router.post(
    '/check-availability',
    BookingController.validateSpecificAvailability(),
    BookingController.checkSpecificAvailability
);

/**
 * GET /api/bookings/facilities/sirens
 * Get Sirens FC facility details
 */
router.get(
    '/facilities/sirens',
    BookingController.getSirensFacilityDetails
);

/**
 * POST /api/bookings
 * Create a new booking (requires authentication)
 */
router.post(
    '/',
    verifyToken,
    requireAuth,
    BookingController.validateBookingCreation(),
    BookingController.createBooking
);

/**
 * GET /api/bookings/user
 * Get user's bookings (requires authentication)
 */
router.get(
    '/user',
    verifyToken,
    requireAuth,
    BookingController.getUserBookings
);

/**
 * PUT /api/bookings/:bookingId/cancel
 * Cancel a booking (requires authentication)
 */
router.put(
    '/:bookingId/cancel',
    verifyToken,
    requireAuth,
    BookingController.cancelBooking
);

module.exports = router;