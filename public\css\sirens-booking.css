/* Sirens FC Booking Page Styles */
.sirens-booking {
    padding: 2rem 0 4rem;
    background-color: #f8f9fa;
    min-height: calc(100vh - 180px);
}

.sirens-booking .page-title {
    color: #000000;
    margin-bottom: 2rem;
    font-size: 2rem;
    font-weight: 700;
    text-align: center;
}

.booking-container {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 2rem auto 0;
    align-items: start;
}

/* Left Column - Calendar and Form */
.booking-form-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-top: 1.5rem;
}

.booking-form h3 {
    color: #1a1a1a;
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.75rem;
}

.booking-form h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: #3b82f6;
    border-radius: 3px;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4b5563;
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background-color: #f8fafc;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: white;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.btn-book {
    width: 100%;
    padding: 1rem 1.5rem;
    background: #3b82f6;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-book::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.btn-book:hover {
    background: #2563eb;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-book:hover::before {
    opacity: 1;
}

.btn-book:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(37, 99, 235, 0.3);
}

/* Right Column - Time Slots and Pitch */
.pitch-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-top: 4.5rem; /* Aligns with calendar */
}

/* Calendar Styling */
.calendar-container {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Time Slots Styling */
.time-slots {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.time-slots h3 {
    color: #1a1a1a;
    margin: 0 0 1.25rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.75rem;
}

.time-slots h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: #3b82f6;
    border-radius: 3px;
}

.time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(85px, 1fr));
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.time-slot {
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    padding: 0.75rem 0.5rem;
    text-align: center;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 500;
    color: #334155;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.time-slot::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.time-slot span {
    position: relative;
    z-index: 1;
}

.time-slot:hover {
    transform: translateY(-2px);
    border-color: #bfdbfe;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
}

.time-slot:hover::before {
    opacity: 0.1;
}

.time-slot.selected {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
    transform: translateY(-2px);
}

.time-slot.selected::before {
    opacity: 0;
}

.time-slot.booked {
    background: #f8fafc;
    color: #94a3b8;
    cursor: not-allowed;
    text-decoration: line-through;
    opacity: 0.7;
    border-style: dashed;
}

/* Pitch Container Styling */
.pitch-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-top: 4.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Modern Calendar Styles */
.calendar-container {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    padding: 1.5rem;
    margin-bottom: 2.5rem;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.calendar-container:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0.5rem 0.5rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    position: relative;
    min-height: 60px;
}

.calendar-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1a1a1a;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    white-space: nowrap;
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border-radius: 20px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.calendar-header button {
    background: #f8f9fa;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    z-index: 1;
    color: #4a4a4a;
    font-size: 1.1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.calendar-header button:hover {
    background: #2196F3;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

#currentMonth {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 650;
    color: #2d3748;
    letter-spacing: 0.3px;
}

.weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    text-align: center;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #718096;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.5rem;
}

.day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    color: #2d3748;
    position: relative;
    overflow: hidden;
    background: #ffffff;
    border: 1px solid #f0f0f0;
}

.day:not(.disabled):hover {
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border-color: #e2e8f0;
}

.day.today {
    background: #e6f7ff;
    color: #1890ff;
    font-weight: 600;
    border: 1px solid #91d5ff;
}

.day.selected {
    background: #2196F3;
    color: white;
    font-weight: 600;
    border-color: #2196F3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
    transform: scale(1.05);
}

.day.selected:hover {
    background: #1e88e5;
    transform: scale(1.05) translateY(-1px);
}

.day.disabled {
    color: #cbd5e0;
    cursor: not-allowed;
    background: #f8fafc;
    border-color: #f0f4f8;
}

/* Animation for day selection */
@keyframes pop {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.day:active:not(.disabled) {
    animation: pop 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .calendar-container {
        padding: 1rem 0.5rem;
    }
    
    .calendar-header button {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
    
    #currentMonth {
        font-size: 1.1rem;
    }
    
    .weekdays {
        font-size: 0.75rem;
    }
    
    .day {
        font-size: 0.9rem;
    }
}

/* Time Slots */
.time-slots {
    margin: 4.5rem 0 2rem 0;
}

.time-slots h3 {
    margin-top: 2rem;
}

.time-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.time-slot {
    background: #e8f5e9;
    border: 1px solid #c8e6c9;
    border-radius: 5px;
    padding: 0.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.time-slot:hover {
    background: #c8e6c9;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.time-slot.selected {
    background: #4CAF50;
    color: white;
    border-color: #388e3c;
    font-weight: 500;
}

.time-slot.booked {
    background: #f5f5f5;
    color: #9e9e9e;
    cursor: pointer;
    text-decoration: none;
    border: 1px dashed #e0e0e0;
}

.time-slot.booked:hover {
    background: #e0e0e0;
}

/* Booking Form */
.booking-form {
    margin-top: 2rem;
}

.date-picker-container h3.section-title {
    color: #1a1a1a;
    margin: 0 0 1.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.75rem;
}

.date-picker-container h3.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: #3b82f6;
    border-radius: 3px;
}

.booking-form h3,
.time-slots h3 {
    color: #000000;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.25rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.btn-book {
    width: 100%;
    padding: 1rem;
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn-book:hover {
    background-color: #1976D2;
}

/* Football Pitch */
.football-pitch {
    position: relative;
    width: 100%;
    aspect-ratio: 1.5;
    background: #4CAF50;
    border: 4px solid white;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.pitch {
    position: relative;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 50%,
        transparent 100%
    ),
    repeating-linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        transparent 50%,
        transparent 100%
    ),
    linear-gradient(135deg, #2E7D32, #1B5E20);
    background-size: 60px 60px, 60px 60px, 100% 100%;
}

.half {
    position: absolute;
    width: 50%;
    height: 100%;
    top: 0;
    transition: all 0.3s ease;
}

.half.left-half {
    left: 0;
    border-right: 2px dashed rgba(255, 255, 255, 0.5);
}

.half.right-half {
    right: 0;
    border-left: 2px dashed rgba(255, 255, 255, 0.5);
}

.center-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30%;
    height: 30%;
    border: 2px solid white;
    border-radius: 50%;
}

.center-line {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
    background: rgba(255, 255, 255, 0.5);
}

.btn-pitch-option,
.btn-pitch-half {
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-pitch-option {
    background: #f0f0f0;
    color: #333;
}

.btn-pitch-option.active,
.btn-pitch-half.active {
    background: #2196F3;
    color: white;
}

.btn-pitch-half {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    padding: 1rem;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.btn-pitch-half.left {
    left: 10px;
}

.btn-pitch-half.right {
    right: 10px;
}

.btn-pitch-half:hover {
    background: rgba(255, 255, 255, 0.3);
}

.btn-pitch-half.active {
    background: #2196F3;
    border-color: #1976D2;
}

/* Pitch Legend */
.pitch-legend {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.legend-color {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 3px;
}

.legend-color.available {
    background: #4CAF50;
}

.legend-color.booked {
    background: #f44336;
}

.legend-color.selected {
    background: #2196F3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-container {
        flex-direction: column;
    }
    
    .booking-form-container,
    .pitch-container {
        min-width: 100%;
    }
    
    .btn-pitch-half {
        padding: 0.75rem;
        font-size: 0.9rem;
    }
}
