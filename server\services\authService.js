const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mockDatabase = require('../../setup-database');

// Use mock database for development
const useMockDatabase = process.env.NODE_ENV === 'development' || !process.env.DB_HOST;

/**
 * Generate JWT token for user
 */
const generateToken = (userId, email) => {
    return jwt.sign(
        { 
            userId, 
            email,
            iat: Math.floor(Date.now() / 1000)
        },
        process.env.JWT_SECRET || 'your-secret-key',
        { 
            expiresIn: process.env.JWT_EXPIRES_IN || '24h'
        }
    );
};

/**
 * Login user with email and password
 */
const loginUser = async (email, password) => {
    try {
        // Find user by email
        const user = await mockDatabase.users.findByEmail(email);
        
        if (!user) {
            throw new Error('Invalid email or password');
        }
        
        if (!user.is_active) {
            throw new Error('Account is inactive');
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password_hash);
        
        if (!isPasswordValid) {
            throw new Error('Invalid email or password');
        }

        // Update last login
        await mockDatabase.users.updateLastLogin(user.id);

        // Generate token
        const token = generateToken(user.id, user.email);

        return {
            success: true,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone
            },
            token
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
};

/**
 * Register new user
 */
const registerUser = async (userData) => {
    try {
        const { email, password, firstName, lastName, phone } = userData;
        
        // Check if user already exists
        const existingUser = await mockDatabase.users.findByEmail(email);
        
        if (existingUser) {
            throw new Error('User with this email already exists');
        }

        // Hash password
        const saltRounds = 10;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Create new user
        const newUser = await mockDatabase.users.create({
            email,
            password_hash: passwordHash,
            firstName,
            lastName,
            phone
        });
        
        return {
            success: true,
            user: {
                id: newUser.id,
                email: newUser.email,
                firstName: newUser.first_name,
                lastName: newUser.last_name,
                phone: newUser.phone
            },
            message: 'Registration successful. Please login to continue.'
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
};

/**
 * Verify user token and return user info
 */
const verifyUserToken = async (token) => {
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        
        // Get user from mock database
        const user = await mockDatabase.users.findByEmail(decoded.email);
        
        if (!user || !user.is_active) {
            throw new Error('Invalid or inactive user');
        }
        
        return {
            success: true,
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                phone: user.phone
            }
        };
    } catch (error) {
        return {
            success: false,
            error: error.message
        };
    }
};

module.exports = {
    loginUser,
    registerUser,
    verifyUserToken,
    generateToken
};