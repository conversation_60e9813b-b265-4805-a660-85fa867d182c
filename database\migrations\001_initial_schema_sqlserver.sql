-- Create database
CREATE DATABASE sports_booking;
GO

USE sports_booking;
GO

-- Create users table
CREATE TABLE users (
    id INT IDENTITY(1,1) PRIMARY KEY,
    first_name NVARCHAR(100) NOT NULL,
    last_name NVARCHAR(100) NOT NULL,
    email NVARCHAR(255) UNIQUE NOT NULL,
    phone NVARCHAR(20) NOT NULL,
    password NVARCHAR(255) NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);

-- Create facilities table
CREATE TABLE facilities (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(255) NOT NULL,
    type NVARCHAR(50) NOT NULL,
    location NVARCHAR(255) NOT NULL,
    description NTEXT,
    pricing NVARCHAR(MAX), -- JSON string
    created_at DATETIME2 DEFAULT GETDATE()
);

-- Create bookings table
CREATE TABLE bookings (
    id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT FOREIGN KEY REFERENCES users(id),
    facility_id INT FOREIGN KEY REFERENCES facilities(id),
    booking_reference NVARCHAR(50) UNIQUE NOT NULL,
    date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    pitch_configuration NVARCHAR(20) NOT NULL,
    status NVARCHAR(20) DEFAULT 'confirmed',
    notes NTEXT,
    created_at DATETIME2 DEFAULT GETDATE()
);

-- Insert sample facility (Sirens)
INSERT INTO facilities (name, type, location, description, pricing) 
VALUES ('Sirens FC', 'football', 'St. Paul''s Bay, Malta', 'Professional football facility with multiple pitch configurations', '{"full": 25, "half": 15}');

-- Create indexes for performance
CREATE INDEX IX_users_email ON users(email);
CREATE INDEX IX_bookings_date ON bookings(date);
CREATE INDEX IX_bookings_facility_date ON bookings(facility_id, date);