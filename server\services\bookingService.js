const { Booking, Facility, User } = require('../models');
const { pool } = require('../config/database');
const mockDatabase = require('../../setup-database');

class BookingService {
    /**
     * Check availability for a specific facility, date, time, and pitch configuration
     */
    static async checkAvailability(facilityId, date, startTime, endTime, pitchConfiguration = 'full') {
        try {
            // Get facility to ensure it exists and is active
            const facility = await Facility.findById(facilityId);
            if (!facility) {
                throw new Error('Facility not found');
            }

            // Check if facility is open at the requested time
            if (!facility.isOpenAt(date, startTime)) {
                return {
                    available: false,
                    reason: 'Facility is closed at the requested time'
                };
            }

            // Find conflicting bookings
            const conflictingBookings = await Booking.findConflictingBookings(
                facilityId, 
                date, 
                startTime, 
                endTime, 
                pitchConfiguration
            );

            if (conflictingBookings.length > 0) {
                return {
                    available: false,
                    reason: 'Time slot is already booked',
                    conflictingBookings: conflictingBookings.map(booking => ({
                        id: booking.id,
                        startTime: booking.startTime,
                        endTime: booking.endTime,
                        pitchConfiguration: this.extractPitchConfiguration(booking.specialRequests)
                    }))
                };
            }

            return {
                available: true,
                facility: {
                    id: facility.id,
                    name: facility.name,
                    sportType: facility.sportType
                }
            };
        } catch (error) {
            console.error('Error checking availability:', error);
            throw error;
        }
    }

    /**
     * Get availability for a specific date and facility
     */
    static async getDateAvailability(facilityId, date) {
        try {
            const facility = await Facility.findById(facilityId);
            if (!facility) {
                throw new Error('Facility not found');
            }

            // Get all time slots for the day
            const timeSlots = facility.getAvailableTimeSlots(date);
            
            // Get all bookings for this facility and date
            const existingBookings = await this.getBookingsForDate(facilityId, date);
            
            // Mark unavailable slots
            const availabilitySlots = timeSlots.map(slot => {
                const availability = this.calculateSlotAvailability(slot, existingBookings);
                
                return {
                    time: slot.startTime,
                    endTime: slot.endTime,
                    duration: slot.duration,
                    availability: availability,
                    pricing: slot.pricing
                };
            });

            return {
                date,
                facilityId,
                facilityName: facility.name,
                timeSlots: availabilitySlots
            };
        } catch (error) {
            console.error('Error getting date availability:', error);
            throw error;
        }
    }

    /**
     * Get bookings for a specific facility and date
     */
    static async getBookingsForDate(facilityId, date) {
        const query = `
            SELECT * FROM bookings 
            WHERE facility_id = $1 
            AND booking_date = $2 
            AND status != 'cancelled'
            ORDER BY start_time
        `;
        
        try {
            const result = await pool.query(query, [facilityId, date]);
            return result.rows.map(row => new Booking(row));
        } catch (error) {
            console.error('Error getting bookings for date:', error);
            throw error;
        }
    }

    /**
     * Calculate availability for a specific time slot
     */
    static calculateSlotAvailability(slot, existingBookings) {
        const slotStart = slot.startTime;
        const slotEnd = slot.endTime;
        
        const availability = {
            full: true,
            left: true,
            right: true
        };

        // Check each existing booking for conflicts
        existingBookings.forEach(booking => {
            const bookingStart = booking.startTime;
            const bookingEnd = booking.endTime;
            
            // Check if times overlap
            if (bookingStart < slotEnd && bookingEnd > slotStart) {
                const pitchConfig = this.extractPitchConfiguration(booking.specialRequests);
                
                if (pitchConfig === 'full') {
                    // Full pitch booking blocks everything
                    availability.full = false;
                    availability.left = false;
                    availability.right = false;
                } else if (pitchConfig === 'left') {
                    // Left side booking blocks left and full
                    availability.left = false;
                    availability.full = false;
                } else if (pitchConfig === 'right') {
                    // Right side booking blocks right and full
                    availability.right = false;
                    availability.full = false;
                }
            }
        });

        return availability;
    }

    /**
     * Extract pitch configuration from special requests
     */
    static extractPitchConfiguration(specialRequests) {
        if (!specialRequests) return 'full';
        
        const lowerRequests = specialRequests.toLowerCase();
        if (lowerRequests.includes('left')) return 'left';
        if (lowerRequests.includes('right')) return 'right';
        if (lowerRequests.includes('full')) return 'full';
        
        return 'full'; // Default to full if not specified
    }

    /**
     * Create a new booking
     */
    static async createBooking(bookingData, userId) {
        try {
            // Calculate duration
            const startTime = new Date(`2000-01-01T${bookingData.startTime}`);
            const endTime = new Date(`2000-01-01T${bookingData.endTime}`);
            const durationHours = (endTime - startTime) / (1000 * 60 * 60);

            // Calculate cost (simple calculation for demo)
            const hourlyRate = 25; // €25 per hour
            const totalCost = durationHours * hourlyRate;

            // Create booking data
            const newBooking = {
                user_id: userId,
                facility_id: bookingData.facilityId || 'sirens-main',
                booking_date: bookingData.date,
                start_time: bookingData.startTime,
                end_time: bookingData.endTime,
                duration_hours: durationHours,
                total_cost: totalCost,
                pitch_configuration: bookingData.pitchConfiguration,
                special_requests: bookingData.notes,
                status: 'confirmed'
            };

            // Save booking using mock database
            const savedBooking = await mockDatabase.bookings.create(newBooking);

            return {
                success: true,
                booking: {
                    id: savedBooking.id,
                    reference: savedBooking.booking_reference,
                    date: savedBooking.booking_date,
                    startTime: savedBooking.start_time,
                    endTime: savedBooking.end_time,
                    pitchConfiguration: savedBooking.pitch_configuration,
                    totalCost: savedBooking.total_cost,
                    status: savedBooking.status,
                    facilityName: 'Sirens FC Sports Complex'
                }
            };
        } catch (error) {
            console.error('Error creating booking:', error);
            throw error;
        }
    }

    /**
     * Generate unique booking reference
     */
    static generateBookingReference(date, time) {
        const dateStr = date.replace(/-/g, '');
        const timeStr = time.replace(':', '');
        const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        return `SFC-${dateStr}-${timeStr}00-${randomSuffix}`;
    }

    /**
     * Update availability cache for a specific facility and date
     */
    static async updateAvailabilityCache(facilityId, date) {
        try {
            const availability = await this.getDateAvailability(facilityId, date);
            
            const query = `
                INSERT INTO availability (facility_id, date, time_slots, last_updated)
                VALUES ($1, $2, $3, CURRENT_TIMESTAMP)
                ON CONFLICT (facility_id, date)
                DO UPDATE SET 
                    time_slots = EXCLUDED.time_slots,
                    last_updated = CURRENT_TIMESTAMP
            `;
            
            await pool.query(query, [
                facilityId,
                date,
                JSON.stringify(availability.timeSlots)
            ]);
        } catch (error) {
            console.error('Error updating availability cache:', error);
            // Don't throw error as this is not critical
        }
    }

    /**
     * Get cached availability or generate fresh data
     */
    static async getCachedAvailability(facilityId, date) {
        try {
            // Check cache first
            const cacheQuery = `
                SELECT time_slots, last_updated 
                FROM availability 
                WHERE facility_id = $1 AND date = $2
                AND last_updated > NOW() - INTERVAL '5 minutes'
            `;
            
            const cacheResult = await pool.query(cacheQuery, [facilityId, date]);
            
            if (cacheResult.rows.length > 0) {
                return {
                    date,
                    facilityId,
                    timeSlots: cacheResult.rows[0].time_slots,
                    cached: true
                };
            }

            // Generate fresh data
            const availability = await this.getDateAvailability(facilityId, date);
            
            // Update cache
            await this.updateAvailabilityCache(facilityId, date);
            
            return {
                ...availability,
                cached: false
            };
        } catch (error) {
            console.error('Error getting cached availability:', error);
            // Fallback to fresh data
            return await this.getDateAvailability(facilityId, date);
        }
    }

    /**
     * Cancel a booking
     */
    static async cancelBooking(bookingId, userId, reason = null) {
        try {
            const booking = await Booking.findById(bookingId);
            
            if (!booking) {
                throw new Error('Booking not found');
            }

            if (booking.userId !== userId) {
                throw new Error('Unauthorized to cancel this booking');
            }

            if (booking.status === 'cancelled') {
                throw new Error('Booking is already cancelled');
            }

            // Check if booking can be cancelled (e.g., not too close to start time)
            const bookingDateTime = new Date(`${booking.bookingDate}T${booking.startTime}`);
            const now = new Date();
            const hoursUntilBooking = (bookingDateTime - now) / (1000 * 60 * 60);

            if (hoursUntilBooking < 2) {
                throw new Error('Cannot cancel booking less than 2 hours before start time');
            }

            // Cancel the booking
            const cancelledBooking = await booking.cancel(reason);

            // Update availability cache
            await this.updateAvailabilityCache(booking.facilityId, booking.bookingDate);

            return {
                success: true,
                booking: cancelledBooking
            };
        } catch (error) {
            console.error('Error cancelling booking:', error);
            throw error;
        }
    }

    /**
     * Get user's bookings with pagination
     */
    static async getUserBookings(userId, options = {}) {
        const { limit = 20, offset = 0, status = null, upcoming = false } = options;
        
        let query = `
            SELECT b.*, f.name as facility_name, v.name as venue_name
            FROM bookings b
            JOIN facilities f ON b.facility_id = f.id
            JOIN venues v ON f.venue_id = v.id
            WHERE b.user_id = $1
        `;
        
        const params = [userId];
        let paramCount = 2;

        if (status) {
            query += ` AND b.status = $${paramCount}`;
            params.push(status);
            paramCount++;
        }

        if (upcoming) {
            query += ` AND b.booking_date >= CURRENT_DATE AND b.status != 'cancelled'`;
        }

        query += ` ORDER BY b.booking_date DESC, b.start_time DESC LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
        params.push(limit, offset);

        try {
            // Use mock database for development
            const userBookings = await mockDatabase.bookings.findByUserId(userId);
            
            // Apply filters
            let filteredBookings = userBookings;
            
            if (status) {
                filteredBookings = filteredBookings.filter(booking => booking.status === status);
            }
            
            if (upcoming) {
                const today = new Date().toISOString().split('T')[0];
                filteredBookings = filteredBookings.filter(booking => 
                    booking.booking_date >= today && booking.status !== 'cancelled'
                );
            }
            
            // Sort by date (newest first)
            filteredBookings.sort((a, b) => new Date(b.booking_date) - new Date(a.booking_date));
            
            // Apply pagination
            const paginatedBookings = filteredBookings.slice(offset, offset + limit);
            
            // Add facility details
            return paginatedBookings.map(booking => ({
                id: booking.id,
                booking_reference: booking.booking_reference,
                booking_date: booking.booking_date,
                start_time: booking.start_time,
                end_time: booking.end_time,
                duration_hours: booking.duration_hours,
                total_price: booking.total_cost,
                status: booking.status,
                facility_name: 'Sirens FC Sports Complex',
                venue_name: 'Sirens FC',
                sport: 'Football',
                pitch_configuration: booking.pitch_configuration,
                pitch_type: booking.pitch_configuration === 'full' ? 'Full Pitch' : 
                           booking.pitch_configuration === 'left' ? 'Left Half' : 'Right Half',
                special_requests: booking.special_requests,
                created_at: booking.created_at
            }));
        } catch (error) {
            console.error('Error getting user bookings:', error);
            throw error;
        }
    }
}

module.exports = BookingService;