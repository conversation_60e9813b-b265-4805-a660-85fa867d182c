/**
 * Test script to verify email functionality works with booking creation
 */
const BookingController = require('./server/controllers/bookingController');

// Mock request and response objects
const mockReq = {
    user: {
        id: 'test-user-123',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User'
    },
    body: {
        facilityId: 'sirens-main',
        date: '2025-08-01',
        startTime: '10:00',
        endTime: '11:00',
        pitchConfiguration: 'full',
        notes: 'Test booking for email functionality'
    }
};

const mockRes = {
    status: function(code) {
        this.statusCode = code;
        return this;
    },
    json: function(data) {
        console.log('Response Status:', this.statusCode);
        console.log('Response Data:', JSON.stringify(data, null, 2));
        return this;
    }
};

// Test the booking creation with email
async function testBookingWithEmail() {
    console.log('🧪 Testing booking creation with email notifications...\n');
    
    try {
        await BookingController.createBooking(mockReq, mockRes);
        console.log('\n✅ Test completed successfully!');
        console.log('📧 Check the console logs above for email notification status.');
        console.log('📧 Admin email should be sent to: <EMAIL>');
        console.log('📧 User confirmation should be sent to: <EMAIL>');
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

// Run the test
testBookingWithEmail();
