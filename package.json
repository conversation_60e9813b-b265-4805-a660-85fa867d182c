{"name": "sports-booking-platform", "version": "1.0.0", "description": "A comprehensive multi-sport facility booking platform for Malta", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "nodemon server/index.js", "client:dev": "live-server public --port=3000", "build": "npm run client:build", "client:build": "webpack --mode=production", "start": "node server/index.js", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["sports", "booking", "platform", "malta", "football", "tennis", "padel"], "author": "Sports Malta", "license": "MIT", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "express-validator": "^7.2.1", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "pg": "^8.11.3", "redis": "^4.6.8", "socket.io": "^4.7.2", "stripe": "^13.5.0", "uuid": "^9.0.0"}, "devDependencies": {"@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "babel-jest": "^29.6.2", "concurrently": "^8.2.0", "jest": "^29.6.2", "jest-environment-jsdom": "^30.0.5", "live-server": "^1.2.2", "nodemon": "^3.0.1", "supertest": "^6.3.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}