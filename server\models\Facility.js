const { pool } = require('../config/database');

class Facility {
    constructor(data) {
        this.id = data.id;
        this.venueId = data.venue_id;
        this.name = data.name;
        this.sportType = data.sport_type;
        this.facilityType = data.facility_type;
        this.capacity = data.capacity;
        this.features = data.features || [];
        this.surfaceType = data.surface_type;
        this.hourlyRatePeak = parseFloat(data.hourly_rate_peak || 0);
        this.hourlyRateOffPeak = parseFloat(data.hourly_rate_off_peak || 0);
        this.hourlyRateWeekend = parseFloat(data.hourly_rate_weekend || 0);
        this.operatingHours = data.operating_hours || {};
        this.isActive = data.is_active;
        this.createdAt = data.created_at;
        this.updatedAt = data.updated_at;
    }

    /**
     * Get facility by ID
     */
    static async findById(id) {
        const query = 'SELECT * FROM facilities WHERE id = $1 AND is_active = true';
        
        try {
            const result = await pool.query(query, [id]);
            if (result.rows.length === 0) {
                return null;
            }
            
            return new Facility(result.rows[0]);
        } catch (error) {
            console.error('Error finding facility:', error);
            throw error;
        }
    }

    /**
     * Get Sirens FC football facilities
     */
    static async getSirensFootballFacilities() {
        const query = `
            SELECT f.* FROM facilities f
            JOIN venues v ON f.venue_id = v.id
            WHERE v.name = 'Sirens FC Sports Complex'
            AND f.sport_type = 'football'
            AND f.is_active = true
            ORDER BY f.name
        `;
        
        try {
            const result = await pool.query(query);
            return result.rows.map(row => new Facility(row));
        } catch (error) {
            console.error('Error finding Sirens facilities:', error);
            throw error;
        }
    }

    /**
     * Get the main Sirens FC football facility (first available)
     */
    static async getSirensMainFootballFacility() {
        const facilities = await this.getSirensFootballFacilities();
        return facilities.length > 0 ? facilities[0] : null;
    }

    /**
     * Check if facility is open at given date and time
     */
    isOpenAt(date, time) {
        const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' });
        const dayHours = this.operatingHours[dayOfWeek];
        
        if (!dayHours || !dayHours.open || !dayHours.close) {
            return false;
        }

        const openTime = dayHours.open;
        const closeTime = dayHours.close;
        
        return time >= openTime && time <= closeTime;
    }

    /**
     * Calculate booking cost based on date, time, and duration
     */
    calculateCost(date, startTime, durationHours, pitchConfiguration = 'full') {
        const bookingDate = new Date(date);
        const dayOfWeek = bookingDate.getDay(); // 0 = Sunday, 6 = Saturday
        const hour = parseInt(startTime.split(':')[0]);
        
        let hourlyRate;
        
        // Determine rate based on day and time
        if (dayOfWeek === 0 || dayOfWeek === 6) { // Weekend
            hourlyRate = this.hourlyRateWeekend;
        } else if (hour >= 17 || hour <= 9) { // Peak hours (before 9 AM or after 5 PM)
            hourlyRate = this.hourlyRatePeak;
        } else { // Off-peak hours
            hourlyRate = this.hourlyRateOffPeak;
        }

        // Apply pitch configuration multiplier
        let configurationMultiplier = 1;
        if (pitchConfiguration === 'left' || pitchConfiguration === 'right') {
            configurationMultiplier = 0.6; // Half pitch is 60% of full pitch cost
        }

        const totalCost = hourlyRate * durationHours * configurationMultiplier;
        return Math.round(totalCost * 100) / 100; // Round to 2 decimal places
    }

    /**
     * Get operating hours for a specific day
     */
    getOperatingHours(date) {
        const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'lowercase' });
        return this.operatingHours[dayOfWeek] || null;
    }

    /**
     * Get available time slots for a specific date
     */
    getAvailableTimeSlots(date) {
        const dayHours = this.getOperatingHours(date);
        if (!dayHours) {
            return [];
        }

        const slots = [];
        const openHour = parseInt(dayHours.open.split(':')[0]);
        const closeHour = parseInt(dayHours.close.split(':')[0]);
        
        // Generate hourly slots
        for (let hour = openHour; hour < closeHour; hour++) {
            const timeString = `${hour.toString().padStart(2, '0')}:00`;
            const endTimeString = `${(hour + 1).toString().padStart(2, '0')}:00`;
            
            slots.push({
                startTime: timeString,
                endTime: endTimeString,
                duration: 1,
                pricing: {
                    full: this.calculateCost(date, timeString, 1, 'full'),
                    half: this.calculateCost(date, timeString, 1, 'left') // Same price for left/right
                }
            });
        }

        return slots;
    }

    /**
     * Validate facility data
     */
    validate() {
        const errors = [];

        if (!this.name || this.name.trim().length === 0) {
            errors.push({ field: 'name', message: 'Facility name is required' });
        }

        if (!this.sportType) {
            errors.push({ field: 'sportType', message: 'Sport type is required' });
        }

        if (!['football', 'tennis', 'padel'].includes(this.sportType)) {
            errors.push({ field: 'sportType', message: 'Invalid sport type' });
        }

        if (this.hourlyRatePeak < 0 || this.hourlyRateOffPeak < 0 || this.hourlyRateWeekend < 0) {
            errors.push({ field: 'rates', message: 'Hourly rates cannot be negative' });
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get facilities by venue ID
     */
    static async findByVenueId(venueId) {
        const query = 'SELECT * FROM facilities WHERE venue_id = $1 AND is_active = true ORDER BY name';
        
        try {
            const result = await pool.query(query, [venueId]);
            return result.rows.map(row => new Facility(row));
        } catch (error) {
            console.error('Error finding facilities by venue:', error);
            throw error;
        }
    }

    /**
     * Get facilities by sport type
     */
    static async findBySportType(sportType) {
        const query = 'SELECT * FROM facilities WHERE sport_type = $1 AND is_active = true ORDER BY name';
        
        try {
            const result = await pool.query(query, [sportType]);
            return result.rows.map(row => new Facility(row));
        } catch (error) {
            console.error('Error finding facilities by sport type:', error);
            throw error;
        }
    }
}

module.exports = Facility;