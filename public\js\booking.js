// Booking Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize booking page
    initializeBookingPage();
    
    // Set minimum date to today
    const dateInput = document.getElementById('quick-date');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
        dateInput.value = today;
    }

    // Quick booking form submission
    const quickBookingForm = document.getElementById('quick-booking-form');
    if (quickBookingForm) {
        quickBookingForm.addEventListener('submit', handleQuickBooking);
    }

    // Sport selection cards
    const sportCards = document.querySelectorAll('.sport-selection-card');
    sportCards.forEach(card => {
        card.addEventListener('click', function() {
            const sport = this.dataset.sport;
            selectSport(sport);
        });
    });
});

function initializeBookingPage() {
    // Check if user came from a specific sport link
    const urlParams = new URLSearchParams(window.location.search);
    const sport = urlParams.get('sport');
    
    if (sport) {
        selectSport(sport);
    }
    
    // Load popular venues
    loadPopularVenues();
}

function selectSport(sport) {
    // Update URL without page reload
    const url = new URL(window.location);
    url.searchParams.set('sport', sport);
    window.history.pushState({}, '', url);
    
    // Highlight selected sport
    const sportCards = document.querySelectorAll('.sport-selection-card');
    sportCards.forEach(card => {
        card.classList.remove('selected');
        if (card.dataset.sport === sport) {
            card.classList.add('selected');
        }
    });
    
    // Update quick booking form
    const quickSportSelect = document.getElementById('quick-sport');
    if (quickSportSelect) {
        quickSportSelect.value = sport;
    }
    
    // Navigate to sport-specific page
    switch (sport) {
        case 'football':
            window.location.href = 'football.html';
            break;
        case 'tennis':
            window.location.href = 'tennis.html';
            break;
        case 'padel':
            window.location.href = 'padel.html';
            break;
        default:
            console.log('Unknown sport:', sport);
    }
}

function handleQuickBooking(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const bookingData = {
        sport: formData.get('sport'),
        location: formData.get('location'),
        date: formData.get('date')
    };
    
    // Validate form data
    if (!bookingData.sport || !bookingData.date) {
        showNotification('Please select a sport and date', 'error');
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Searching...';
    submitBtn.disabled = true;
    
    // Simulate API call to find available slots
    setTimeout(() => {
        // Reset button
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
        
        // Navigate to results page with search parameters
        const searchParams = new URLSearchParams(bookingData);
        window.location.href = `search-results.html?${searchParams.toString()}`;
    }, 1500);
}

async function loadPopularVenues() {
    try {
        // This would normally fetch from API
        // For now, we'll use the static content already in HTML
        console.log('Popular venues loaded from static content');
        
        // Add click tracking for venue cards
        const venueCards = document.querySelectorAll('.venue-card');
        venueCards.forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.closest('.btn-venue')) {
                    const venueLink = this.querySelector('.btn-venue');
                    if (venueLink) {
                        venueLink.click();
                    }
                }
            });
        });
        
    } catch (error) {
        console.error('Error loading popular venues:', error);
    }
}

// Sport selection animation
function animateSportSelection() {
    const cards = document.querySelectorAll('.sport-selection-card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

// Update booking steps
function updateBookingStep(stepNumber) {
    const steps = document.querySelectorAll('.step');
    
    steps.forEach((step, index) => {
        if (index < stepNumber) {
            step.classList.add('completed');
            step.classList.remove('active');
        } else if (index === stepNumber) {
            step.classList.add('active');
            step.classList.remove('completed');
        } else {
            step.classList.remove('active', 'completed');
        }
    });
}

// Filter venues by sport
function filterVenuesBySport(sport) {
    const venueCards = document.querySelectorAll('.venue-card');
    
    venueCards.forEach(card => {
        const sportBadges = card.querySelectorAll('.sport-badge');
        let hasMatchingSport = false;
        
        if (!sport) {
            hasMatchingSport = true; // Show all if no filter
        } else {
            sportBadges.forEach(badge => {
                if (badge.classList.contains(sport)) {
                    hasMatchingSport = true;
                }
            });
        }
        
        if (hasMatchingSport) {
            card.style.display = 'block';
            card.style.animation = 'fadeIn 0.5s ease';
        } else {
            card.style.display = 'none';
        }
    });
}

// Location-based filtering
function filterVenuesByLocation(location) {
    const venueCards = document.querySelectorAll('.venue-card');
    
    venueCards.forEach(card => {
        const venueLocation = card.querySelector('.venue-location').textContent.toLowerCase();
        
        if (!location || location === '' || venueLocation.includes(location.toLowerCase())) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// Add CSS animation for fade in
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .sport-selection-card.selected {
        border-color: var(--primary-color);
        box-shadow: 0 4px 20px rgba(0, 220, 48, 0.2);
        transform: translateY(-5px);
    }
    
    .step.completed .step-number {
        background: var(--primary-color);
        color: white;
    }
    
    .step.completed .step-number::after {
        content: '✓';
        font-size: 0.8rem;
    }
`;
document.head.appendChild(style);

// Export functions for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        selectSport,
        handleQuickBooking,
        loadPopularVenues,
        updateBookingStep,
        filterVenuesBySport,
        filterVenuesByLocation
    };
}