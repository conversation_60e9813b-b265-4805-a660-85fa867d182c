const { query } = require('../config/database');

class User {
    static async findByEmail(email) {
        const result = await query('SELECT * FROM users WHERE email = @param0', [email]);
        return result.rows[0];
    }

    static async findById(id) {
        const result = await query('SELECT * FROM users WHERE id = @param0', [id]);
        return result.rows[0];
    }

    static async create(userData) {
        const { firstName, lastName, email, phone, password } = userData;
        const result = await query(
            `INSERT INTO users (first_name, last_name, email, phone, password) 
             OUTPUT INSERTED.* 
             VALUES (@param0, @param1, @param2, @param3, @param4)`,
            [firstName, lastName, email, phone, password]
        );
        
        const user = result.rows[0];
        return {
            id: user.id,
            firstName: user.first_name,
            lastName: user.last_name,
            email: user.email,
            phone: user.phone
        };
    }
}

module.exports = User;
