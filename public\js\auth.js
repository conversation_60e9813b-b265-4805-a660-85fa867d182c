/**
 * Authentication Handler for Frontend
 * Manages user authentication state and API interactions
 */
class AuthHandler {
    constructor() {
        this.baseURL = '/api/auth';
        this.init();
    }

    init() {
        // Check if we're on login or signup page
        if (document.getElementById('loginForm')) {
            this.initLogin();
        }
        if (document.getElementById('signupForm')) {
            this.initSignup();
        }
    }

    initLogin() {
        const form = document.getElementById('loginForm');
        form.addEventListener('submit', (e) => this.handleLogin(e));
    }

    initSignup() {
        const form = document.getElementById('signupForm');
        form.addEventListener('submit', (e) => this.handleSignup(e));
        
        // Real-time password confirmation validation
        const confirmPassword = document.getElementById('confirmPassword');
        confirmPassword.addEventListener('input', () => this.validatePasswordMatch());
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const btn = document.getElementById('loginBtn');
        const btnText = btn.querySelector('.btn-text');
        const spinner = btn.querySelector('.loading-spinner');
        
        // Clear previous errors
        this.clearErrors();
        
        // Get form data
        const formData = new FormData(e.target);
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };

        // Validate
        if (!this.validateLogin(loginData)) {
            return;
        }

        // Show loading state
        btn.disabled = true;
        btnText.style.display = 'none';
        spinner.style.display = 'block';

        try {
            const response = await fetch(`${this.baseURL}/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(loginData)
            });

            const result = await response.json();

            if (result.success) {
                // Store token
                localStorage.setItem('authToken', result.data.token);
                localStorage.setItem('user', JSON.stringify(result.data.user));
                
                // Redirect to intended page or home
                const returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/';
                window.location.href = returnUrl;
            } else {
                this.showError(result.error.message);
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Login failed. Please try again.');
        } finally {
            // Reset button state
            btn.disabled = false;
            btnText.style.display = 'block';
            spinner.style.display = 'none';
        }
    }

    async handleSignup(e) {
        e.preventDefault();
        
        const btn = document.getElementById('signupBtn');
        const btnText = btn.querySelector('.btn-text');
        const spinner = btn.querySelector('.loading-spinner');
        
        // Clear previous errors
        this.clearErrors();
        
        // Get form data
        const formData = new FormData(e.target);
        const signupData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            email: formData.get('email'),
            phone: formData.get('phone'),
            password: formData.get('password'),
            confirmPassword: formData.get('confirmPassword')
        };

        // Validate
        if (!this.validateSignup(signupData)) {
            return;
        }

        // Show loading state
        btn.disabled = true;
        btnText.style.display = 'none';
        spinner.style.display = 'block';

        try {
            const response = await fetch(`${this.baseURL}/signup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(signupData)
            });

            const result = await response.json();

            if (result.success) {
                // Store token
                localStorage.setItem('authToken', result.data.token);
                localStorage.setItem('user', JSON.stringify(result.data.user));
                
                // Redirect to home or intended page
                const returnUrl = new URLSearchParams(window.location.search).get('returnUrl') || '/';
                window.location.href = returnUrl;
            } else {
                if (result.error.details) {
                    this.showFieldErrors(result.error.details);
                } else {
                    this.showError(result.error.message);
                }
            }
        } catch (error) {
            console.error('Signup error:', error);
            this.showError('Signup failed. Please try again.');
        } finally {
            // Reset button state
            btn.disabled = false;
            btnText.style.display = 'block';
            spinner.style.display = 'none';
        }
    }

    validateLogin(data) {
        let isValid = true;

        if (!data.email) {
            this.showFieldError('email', 'Email is required');
            isValid = false;
        } else if (!this.isValidEmail(data.email)) {
            this.showFieldError('email', 'Please enter a valid email');
            isValid = false;
        }

        if (!data.password) {
            this.showFieldError('password', 'Password is required');
            isValid = false;
        }

        return isValid;
    }

    validateSignup(data) {
        let isValid = true;

        if (!data.firstName) {
            this.showFieldError('firstName', 'First name is required');
            isValid = false;
        }

        if (!data.lastName) {
            this.showFieldError('lastName', 'Last name is required');
            isValid = false;
        }

        if (!data.email) {
            this.showFieldError('email', 'Email is required');
            isValid = false;
        } else if (!this.isValidEmail(data.email)) {
            this.showFieldError('email', 'Please enter a valid email');
            isValid = false;
        }

        if (!data.phone) {
            this.showFieldError('phone', 'Phone number is required');
            isValid = false;
        }

        if (!data.password) {
            this.showFieldError('password', 'Password is required');
            isValid = false;
        } else if (data.password.length < 6) {
            this.showFieldError('password', 'Password must be at least 6 characters');
            isValid = false;
        }

        if (data.password !== data.confirmPassword) {
            this.showFieldError('confirmPassword', 'Passwords do not match');
            isValid = false;
        }

        return isValid;
    }

    validatePasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        if (confirmPassword && password !== confirmPassword) {
            this.showFieldError('confirmPassword', 'Passwords do not match');
        } else {
            this.clearFieldError('confirmPassword');
        }
    }

    isValidEmail(email) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    }

    showFieldError(fieldName, message) {
        const field = document.getElementById(fieldName);
        const errorElement = document.getElementById(`${fieldName}Error`);
        
        if (field) field.classList.add('error');
        if (errorElement) errorElement.textContent = message;
    }

    clearFieldError(fieldName) {
        const field = document.getElementById(fieldName);
        const errorElement = document.getElementById(`${fieldName}Error`);
        
        if (field) field.classList.remove('error');
        if (errorElement) errorElement.textContent = '';
    }

    showFieldErrors(errors) {
        errors.forEach(error => {
            this.showFieldError(error.path, error.msg);
        });
    }

    clearErrors() {
        const errorElements = document.querySelectorAll('.error-message');
        const fieldElements = document.querySelectorAll('.form-group input');
        const errorBanner = document.getElementById('errorMessage');
        
        errorElements.forEach(el => el.textContent = '');
        fieldElements.forEach(el => el.classList.remove('error'));
        if (errorBanner) errorBanner.style.display = 'none';
    }

    showError(message) {
        const errorBanner = document.getElementById('errorMessage');
        if (errorBanner) {
            errorBanner.textContent = message;
            errorBanner.style.display = 'block';
        }
    }

    // Methods for checking auth status (used by other pages)
    async checkAuthStatus() {
        const token = localStorage.getItem('authToken');
        if (!token) {
            return { success: false };
        }

        try {
            const response = await fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const result = await response.json();
            
            if (result.success) {
                return {
                    success: true,
                    user: result.data.user
                };
            } else {
                // Token is invalid, clear it
                this.logout();
                return { success: false };
            }
        } catch (error) {
            console.error('Auth check failed:', error);
            this.logout();
            return { success: false };
        }
    }

    async makeAuthenticatedRequest(url, options = {}) {
        const token = localStorage.getItem('authToken');
        if (!token) {
            this.redirectToLogin();
            return null;
        }

        const headers = {
            'Authorization': `Bearer ${token}`,
            ...options.headers
        };

        try {
            const response = await fetch(url, {
                ...options,
                headers
            });

            if (response.status === 401) {
                // Token expired or invalid
                this.logout();
                this.redirectToLogin();
                return null;
            }

            return response;
        } catch (error) {
            console.error('Authenticated request failed:', error);
            throw error;
        }
    }

    redirectToLogin(returnUrl = null) {
        const currentUrl = returnUrl || window.location.pathname + window.location.search;
        window.location.href = `/login.html?returnUrl=${encodeURIComponent(currentUrl)}`;
    }

    logout() {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        window.location.href = '/';
    }

    async register(userData) {
        try {
            const response = await fetch(`${this.baseURL}/signup`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Registration error:', error);
            return {
                success: false,
                error: { message: 'Registration failed. Please try again.' }
            };
        }
    }
}

// Initialize auth handler
window.authHandler = new AuthHandler();
