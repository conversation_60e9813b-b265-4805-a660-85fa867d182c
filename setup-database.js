const { Pool } = require('pg');
require('dotenv').config();

// Create a simple in-memory database simulation for development
const users = new Map();
const bookings = new Map();
let userIdCounter = 1;
let bookingIdCounter = 1;

// Mock database functions
const mockDatabase = {
    users: {
        async create(userData) {
            const id = userIdCounter++;
            const user = {
                id: id.toString(),
                email: userData.email.toLowerCase(),
                password_hash: userData.password_hash,
                first_name: userData.firstName,
                last_name: userData.lastName,
                phone: userData.phone || null,
                is_active: true,
                email_verified: false,
                created_at: new Date().toISOString(),
                last_login: null
            };
            users.set(user.email, user);
            return user;
        },

        async findByEmail(email) {
            return users.get(email.toLowerCase()) || null;
        },

        async updateLastLogin(userId) {
            for (let [email, user] of users) {
                if (user.id === userId) {
                    user.last_login = new Date().toISOString();
                    users.set(email, user);
                    break;
                }
            }
        }
    },

    bookings: {
        async create(bookingData) {
            const id = bookingIdCounter++;
            const booking = {
                id: id.toString(),
                ...bookingData,
                booking_reference: `BK${id.toString().padStart(3, '0')}`,
                created_at: new Date().toISOString(),
                status: 'confirmed'
            };
            bookings.set(booking.id, booking);
            return booking;
        },

        async findByUserId(userId) {
            return Array.from(bookings.values()).filter(booking => booking.user_id === userId);
        }
    }
};

// Export the mock database
module.exports = mockDatabase;

console.log('Mock database initialized for development');