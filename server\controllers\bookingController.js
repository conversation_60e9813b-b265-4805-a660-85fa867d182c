const BookingService = require('../services/bookingService');
const FacilityService = require('../services/facilityService');
const { body, param, validationResult } = require('express-validator');

class BookingController {
    /**
     * GET /api/bookings/availability/:facilityId/:date
     * Get availability for a specific facility and date
     */
    static async getAvailability(req, res) {
        try {
            // Validate parameters
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Invalid request parameters',
                        details: errors.array()
                    }
                });
            }

            const { facilityId, date } = req.params;

            // Validate date format
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(date)) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_DATE_FORMAT',
                        message: 'Date must be in YYYY-MM-DD format'
                    }
                });
            }

            // Check if date is not in the past
            const requestedDate = new Date(date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (requestedDate < today) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'PAST_DATE',
                        message: 'Cannot get availability for past dates'
                    }
                });
            }

            // Get availability data
            const availability = await BookingService.getCachedAvailability(facilityId, date);

            res.json({
                success: true,
                data: availability
            });
        } catch (error) {
            console.error('Error getting availability:', error);
            
            if (error.message === 'Facility not found') {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'FACILITY_NOT_FOUND',
                        message: 'Facility not found'
                    }
                });
            }

            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to get availability data'
                }
            });
        }
    }

    /**
     * GET /api/bookings/availability/sirens/:date
     * Get availability for Sirens FC main facility
     */
    static async getSirensAvailability(req, res) {
        try {
            const { date } = req.params;

            // Validate date format
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(date)) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_DATE_FORMAT',
                        message: 'Date must be in YYYY-MM-DD format'
                    }
                });
            }

            // Get Sirens main facility
            const facility = await FacilityService.getSirensMainFacility();
            
            // Get availability data
            const availability = await BookingService.getCachedAvailability(facility.id, date);

            res.json({
                success: true,
                data: {
                    ...availability,
                    facilityDetails: {
                        id: facility.id,
                        name: facility.name,
                        sportType: facility.sportType,
                        features: facility.features
                    }
                }
            });
        } catch (error) {
            console.error('Error getting Sirens availability:', error);
            
            if (error.message === 'Sirens FC football facility not found') {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'SIRENS_FACILITY_NOT_FOUND',
                        message: 'Sirens FC facility not found'
                    }
                });
            }

            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to get Sirens availability data'
                }
            });
        }
    }

    /**
     * POST /api/bookings/check-availability
     * Check availability for specific time slot and pitch configuration
     */
    static async checkSpecificAvailability(req, res) {
        try {
            // Validate request body
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Invalid request data',
                        details: errors.array()
                    }
                });
            }

            const { facilityId, date, startTime, endTime, pitchConfiguration } = req.body;

            // Check availability
            const availability = await BookingService.checkAvailability(
                facilityId,
                date,
                startTime,
                endTime,
                pitchConfiguration
            );

            if (availability.available) {
                // Get facility details for cost calculation
                const facility = await FacilityService.getFacilityById(facilityId);
                const startDateTime = new Date(`2000-01-01T${startTime}`);
                const endDateTime = new Date(`2000-01-01T${endTime}`);
                const durationHours = (endDateTime - startDateTime) / (1000 * 60 * 60);

                const costDetails = FacilityService.calculateBookingCost(
                    facility,
                    date,
                    startTime,
                    durationHours,
                    pitchConfiguration
                );

                res.json({
                    success: true,
                    available: true,
                    data: {
                        facility: availability.facility,
                        timeSlot: {
                            date,
                            startTime,
                            endTime,
                            duration: durationHours,
                            pitchConfiguration
                        },
                        pricing: costDetails
                    }
                });
            } else {
                res.json({
                    success: true,
                    available: false,
                    reason: availability.reason,
                    conflictingBookings: availability.conflictingBookings || []
                });
            }
        } catch (error) {
            console.error('Error checking specific availability:', error);
            
            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to check availability'
                }
            });
        }
    }

    /**
     * GET /api/bookings/facilities/sirens
     * Get Sirens FC facility details
     */
    static async getSirensFacilityDetails(req, res) {
        try {
            const facility = await FacilityService.getSirensMainFacility();
            const details = await FacilityService.getFacilityDetails(facility.id);

            res.json({
                success: true,
                data: {
                    ...details,
                    supportedConfigurations: FacilityService.getAvailablePitchConfigurations(facility)
                }
            });
        } catch (error) {
            console.error('Error getting Sirens facility details:', error);
            
            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to get facility details'
                }
            });
        }
    }

    /**
     * Validation middleware for availability endpoints
     */
    static validateAvailabilityParams() {
        return [
            param('facilityId')
                .isUUID()
                .withMessage('Facility ID must be a valid UUID'),
            param('date')
                .matches(/^\d{4}-\d{2}-\d{2}$/)
                .withMessage('Date must be in YYYY-MM-DD format')
        ];
    }

    /**
     * POST /api/bookings
     * Create a new booking
     */
    static async createBooking(req, res) {
        try {
            // Validate request body
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'VALIDATION_ERROR',
                        message: 'Invalid booking data',
                        details: errors.array()
                    }
                });
            }

            const userId = req.user.id;
            const bookingData = req.body;

            // Validate that the date is not in the past
            const bookingDate = new Date(bookingData.date);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (bookingDate < today) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'PAST_DATE',
                        message: 'Cannot create booking for past dates'
                    }
                });
            }

            // Validate time sequence
            const startTime = new Date(`2000-01-01T${bookingData.startTime}`);
            const endTime = new Date(`2000-01-01T${bookingData.endTime}`);

            if (startTime >= endTime) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'INVALID_TIME_RANGE',
                        message: 'End time must be after start time'
                    }
                });
            }

            // Create the booking
            const result = await BookingService.createBooking(bookingData, userId);

            res.status(201).json({
                success: true,
                message: 'Booking created successfully',
                data: result.booking
            });
        } catch (error) {
            console.error('Error creating booking:', error);
            
            if (error.message.includes('not available')) {
                return res.status(409).json({
                    success: false,
                    error: {
                        code: 'BOOKING_CONFLICT',
                        message: error.message
                    }
                });
            }

            if (error.message === 'User not found') {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'USER_NOT_FOUND',
                        message: 'User account not found'
                    }
                });
            }

            if (error.message === 'Facility not found') {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'FACILITY_NOT_FOUND',
                        message: 'Facility not found'
                    }
                });
            }

            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to create booking'
                }
            });
        }
    }

    /**
     * GET /api/bookings/user
     * Get user's bookings
     */
    static async getUserBookings(req, res) {
        try {
            const userId = req.user.id;
            const { limit = 20, offset = 0, status, upcoming } = req.query;

            const options = {
                limit: parseInt(limit),
                offset: parseInt(offset),
                status: status || null,
                upcoming: upcoming === 'true'
            };

            const bookings = await BookingService.getUserBookings(userId, options);

            res.json({
                success: true,
                bookings: bookings,
                pagination: {
                    limit: options.limit,
                    offset: options.offset,
                    total: bookings.length
                }
            });
        } catch (error) {
            console.error('Error getting user bookings:', error);
            
            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to get user bookings'
                }
            });
        }
    }

    /**
     * PUT /api/bookings/:bookingId/cancel
     * Cancel a booking
     */
    static async cancelBooking(req, res) {
        try {
            const { bookingId } = req.params;
            const userId = req.user.id;
            const { reason } = req.body;

            const result = await BookingService.cancelBooking(bookingId, userId, reason);

            res.json({
                success: true,
                message: 'Booking cancelled successfully',
                data: result.booking
            });
        } catch (error) {
            console.error('Error cancelling booking:', error);
            
            if (error.message === 'Booking not found') {
                return res.status(404).json({
                    success: false,
                    error: {
                        code: 'BOOKING_NOT_FOUND',
                        message: 'Booking not found'
                    }
                });
            }

            if (error.message === 'Unauthorized to cancel this booking') {
                return res.status(403).json({
                    success: false,
                    error: {
                        code: 'UNAUTHORIZED',
                        message: 'You are not authorized to cancel this booking'
                    }
                });
            }

            if (error.message.includes('Cannot cancel booking')) {
                return res.status(400).json({
                    success: false,
                    error: {
                        code: 'CANCELLATION_NOT_ALLOWED',
                        message: error.message
                    }
                });
            }

            res.status(500).json({
                success: false,
                error: {
                    code: 'SERVER_ERROR',
                    message: 'Failed to cancel booking'
                }
            });
        }
    }

    /**
     * Validation middleware for specific availability check
     */
    static validateSpecificAvailability() {
        return [
            body('facilityId')
                .isUUID()
                .withMessage('Facility ID must be a valid UUID'),
            body('date')
                .matches(/^\d{4}-\d{2}-\d{2}$/)
                .withMessage('Date must be in YYYY-MM-DD format'),
            body('startTime')
                .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
                .withMessage('Start time must be in HH:MM format'),
            body('endTime')
                .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
                .withMessage('End time must be in HH:MM format'),
            body('pitchConfiguration')
                .isIn(['full', 'left', 'right'])
                .withMessage('Pitch configuration must be full, left, or right')
        ];
    }

    /**
     * Validation middleware for booking creation
     */
    static validateBookingCreation() {
        return [
            body('facilityId')
                .isUUID()
                .withMessage('Facility ID must be a valid UUID'),
            body('date')
                .matches(/^\d{4}-\d{2}-\d{2}$/)
                .withMessage('Date must be in YYYY-MM-DD format'),
            body('startTime')
                .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
                .withMessage('Start time must be in HH:MM format'),
            body('endTime')
                .matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
                .withMessage('End time must be in HH:MM format'),
            body('pitchConfiguration')
                .isIn(['full', 'left', 'right'])
                .withMessage('Pitch configuration must be full, left, or right'),
            body('notes')
                .optional()
                .isLength({ max: 500 })
                .withMessage('Notes must be less than 500 characters')
        ];
    }
}

module.exports = BookingController;