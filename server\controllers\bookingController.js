const BookingService = require('../services/bookingService');
const EmailService = require('../services/emailService');
const { body, param, validationResult } = require('express-validator');

class BookingController {
    /**
     * POST /api/bookings
     * Create a new booking with email notifications
     */
    static async createBooking(req, res) {
        try {
            console.log('🎯 Booking request:', req.body);

            const userId = req.user?.id || req.user?.userId || 'guest';
            const userEmail = req.user?.email || '<EMAIL>';

            const bookingData = {
                userId: userId,
                userEmail: userEmail,
                ...req.body
            };

            // Validate that the date is not in the past
            if (bookingData.date) {
                const bookingDate = new Date(bookingData.date);
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (bookingDate < today) {
                    return res.status(400).json({
                        success: false,
                        error: 'Cannot create booking for past dates'
                    });
                }
            }

            // Validate time sequence
            if (bookingData.startTime && bookingData.endTime) {
                const startTime = new Date(`2000-01-01T${bookingData.startTime}`);
                const endTime = new Date(`2000-01-01T${bookingData.endTime}`);

                if (startTime >= endTime) {
                    return res.status(400).json({
                        success: false,
                        error: 'End time must be after start time'
                    });
                }
            }

            // Create the booking
            const result = BookingService.createBooking(bookingData);

            // Get user details for email
            const userData = {
                id: userId,
                email: userEmail,
                firstName: req.user?.firstName || 'User',
                lastName: req.user?.lastName || '',
                phone: req.user?.phone || ''
            };

            // Prepare booking data for email templates
            const emailBookingData = {
                ...result.booking,
                bookingReference: result.booking.reference || result.bookingReference,
                totalPrice: result.booking.totalCost || 50.00,
                facilityName: 'Sirens FC Sports Complex',
                date: bookingData.date,
                startTime: bookingData.startTime,
                endTime: bookingData.endTime,
                pitchConfiguration: bookingData.pitchConfiguration || 'full'
            };

            // Send email notifications (don't wait for completion to avoid blocking response)
            Promise.all([
                EmailService.sendBookingNotificationToAdmin(emailBookingData, userData),
                EmailService.sendBookingConfirmationToUser(emailBookingData, userData)
            ]).then(([adminResult, userResult]) => {
                console.log('Email notifications sent:', {
                    admin: adminResult.success ? 'Success' : 'Failed',
                    user: userResult.success ? 'Success' : 'Failed'
                });
            }).catch(error => {
                console.error('Error sending email notifications:', error);
            });

            res.status(201).json({
                success: true,
                message: 'Booking created successfully!',
                data: result.booking
            });

        } catch (error) {
            console.error('❌ Booking error:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to create booking'
            });
        }
    }

    /**
     * GET /api/bookings/user
     * Get user's bookings
     */
    static async getUserBookings(req, res) {
        try {
            const userId = req.user?.id || req.user?.userId || 'guest';
            const bookings = BookingService.getUserBookings(userId);
            res.json({
                success: true,
                data: bookings
            });
        } catch (error) {
            console.error('❌ Error getting user bookings:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to get user bookings'
            });
        }
    }

    /**
     * GET /api/bookings
     * Get all bookings (admin)
     */
    static async getAllBookings(req, res) {
        try {
            const bookings = BookingService.getAllBookings();
            res.json({
                success: true,
                data: bookings
            });
        } catch (error) {
            console.error('❌ Error getting all bookings:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to get bookings'
            });
        }
    }
}

module.exports = BookingController;