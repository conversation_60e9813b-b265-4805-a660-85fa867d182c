const { pool } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

class Booking {
    constructor(data) {
        this.id = data.id || uuidv4();
        this.userId = data.userId;
        this.facilityId = data.facilityId;
        this.bookingDate = data.bookingDate;
        this.startTime = data.startTime;
        this.endTime = data.endTime;
        this.durationHours = data.durationHours;
        this.totalCost = data.totalCost;
        this.status = data.status || 'pending';
        this.paymentStatus = data.paymentStatus || 'pending';
        this.bookingReference = data.bookingReference;
        this.specialRequests = data.specialRequests;
        this.pitchConfiguration = data.pitchConfiguration; // 'full', 'left', 'right'
    }

    /**
     * Validate booking data
     */
    validate() {
        const errors = [];

        // Required fields validation
        if (!this.userId) {
            errors.push({ field: 'userId', message: 'User ID is required' });
        }

        if (!this.facilityId) {
            errors.push({ field: 'facilityId', message: 'Facility ID is required' });
        }

        if (!this.bookingDate) {
            errors.push({ field: 'bookingDate', message: 'Booking date is required' });
        }

        if (!this.startTime) {
            errors.push({ field: 'startTime', message: 'Start time is required' });
        }

        if (!this.endTime) {
            errors.push({ field: 'endTime', message: 'End time is required' });
        }

        if (!this.pitchConfiguration) {
            errors.push({ field: 'pitchConfiguration', message: 'Pitch configuration is required' });
        }

        // Date validation
        if (this.bookingDate) {
            const bookingDate = new Date(this.bookingDate);
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            if (bookingDate < today) {
                errors.push({ field: 'bookingDate', message: 'Booking date cannot be in the past' });
            }
        }

        // Time validation
        if (this.startTime && this.endTime) {
            const start = new Date(`2000-01-01T${this.startTime}`);
            const end = new Date(`2000-01-01T${this.endTime}`);

            if (start >= end) {
                errors.push({ field: 'endTime', message: 'End time must be after start time' });
            }
        }

        // Pitch configuration validation
        if (this.pitchConfiguration && !['full', 'left', 'right'].includes(this.pitchConfiguration)) {
            errors.push({ field: 'pitchConfiguration', message: 'Invalid pitch configuration' });
        }

        // Duration validation
        if (this.durationHours && (this.durationHours < 0.5 || this.durationHours > 8)) {
            errors.push({ field: 'durationHours', message: 'Duration must be between 0.5 and 8 hours' });
        }

        // Cost validation
        if (this.totalCost && this.totalCost < 0) {
            errors.push({ field: 'totalCost', message: 'Total cost cannot be negative' });
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Calculate duration in hours from start and end time
     */
    calculateDuration() {
        if (!this.startTime || !this.endTime) {
            return 0;
        }

        const start = new Date(`2000-01-01T${this.startTime}`);
        const end = new Date(`2000-01-01T${this.endTime}`);
        
        const diffMs = end - start;
        const diffHours = diffMs / (1000 * 60 * 60);
        
        return Math.round(diffHours * 10) / 10; // Round to 1 decimal place
    }

    /**
     * Generate booking reference
     */
    static generateBookingReference(date, time) {
        const dateStr = date.replace(/-/g, '');
        const timeStr = time.replace(':', '');
        const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        
        return `SFC-${dateStr}-${timeStr}00-${randomSuffix}`;
    }

    /**
     * Save booking to database
     */
    async save() {
        const validation = this.validate();
        if (!validation.isValid) {
            throw new Error(`Validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
        }

        // Calculate duration if not provided
        if (!this.durationHours) {
            this.durationHours = this.calculateDuration();
        }

        // Generate booking reference if not provided
        if (!this.bookingReference) {
            this.bookingReference = Booking.generateBookingReference(this.bookingDate, this.startTime);
        }

        const query = `
            INSERT INTO bookings (
                id, user_id, facility_id, booking_date, start_time, end_time,
                duration_hours, total_cost, status, payment_status, booking_reference,
                special_requests
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
            RETURNING *
        `;

        const specialRequests = this.pitchConfiguration ? 
            `Pitch Configuration: ${this.pitchConfiguration}${this.specialRequests ? '; ' + this.specialRequests : ''}` :
            this.specialRequests;

        const values = [
            this.id,
            this.userId,
            this.facilityId,
            this.bookingDate,
            this.startTime,
            this.endTime,
            this.durationHours,
            this.totalCost,
            this.status,
            this.paymentStatus,
            this.bookingReference,
            specialRequests
        ];

        try {
            const result = await pool.query(query, values);
            return result.rows[0];
        } catch (error) {
            console.error('Error saving booking:', error);
            throw error;
        }
    }

    /**
     * Find booking by ID
     */
    static async findById(id) {
        const query = 'SELECT * FROM bookings WHERE id = $1';
        
        try {
            const result = await pool.query(query, [id]);
            if (result.rows.length === 0) {
                return null;
            }
            
            return new Booking(result.rows[0]);
        } catch (error) {
            console.error('Error finding booking:', error);
            throw error;
        }
    }

    /**
     * Find bookings by user ID
     */
    static async findByUserId(userId) {
        const query = 'SELECT * FROM bookings WHERE user_id = $1 ORDER BY booking_date DESC, start_time DESC';
        
        try {
            const result = await pool.query(query, [userId]);
            return result.rows.map(row => new Booking(row));
        } catch (error) {
            console.error('Error finding user bookings:', error);
            throw error;
        }
    }

    /**
     * Find bookings for a specific facility, date, and time range
     */
    static async findConflictingBookings(facilityId, date, startTime, endTime, pitchConfiguration) {
        let query = `
            SELECT * FROM bookings 
            WHERE facility_id = $1 
            AND booking_date = $2 
            AND status != 'cancelled'
            AND (
                (start_time < $4 AND end_time > $3)
            )
        `;
        
        const values = [facilityId, date, startTime, endTime];

        // If booking a half pitch, only check for conflicts with full pitch or same side
        if (pitchConfiguration === 'left' || pitchConfiguration === 'right') {
            query += ` AND (special_requests LIKE '%full%' OR special_requests LIKE '%${pitchConfiguration}%')`;
        }
        // If booking full pitch, check for any existing bookings
        else if (pitchConfiguration === 'full') {
            // Full pitch conflicts with any booking
        }

        try {
            const result = await pool.query(query, values);
            return result.rows.map(row => new Booking(row));
        } catch (error) {
            console.error('Error finding conflicting bookings:', error);
            throw error;
        }
    }

    /**
     * Update booking status
     */
    async updateStatus(status) {
        const query = 'UPDATE bookings SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *';
        
        try {
            const result = await pool.query(query, [status, this.id]);
            if (result.rows.length > 0) {
                this.status = status;
                return result.rows[0];
            }
            return null;
        } catch (error) {
            console.error('Error updating booking status:', error);
            throw error;
        }
    }

    /**
     * Cancel booking
     */
    async cancel(reason = null) {
        const query = `
            UPDATE bookings 
            SET status = 'cancelled', 
                cancelled_at = CURRENT_TIMESTAMP,
                cancellation_reason = $1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = $2 
            RETURNING *
        `;
        
        try {
            const result = await pool.query(query, [reason, this.id]);
            if (result.rows.length > 0) {
                this.status = 'cancelled';
                return result.rows[0];
            }
            return null;
        } catch (error) {
            console.error('Error cancelling booking:', error);
            throw error;
        }
    }
}

module.exports = Booking;