const sql = require('mssql');

const dbConfig = {
    user: process.env.DB_USER || 'sa',
    password: process.env.DB_PASSWORD || 'YourPassword123!',
    server: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'sports_booking',
    options: {
        encrypt: false, // Use true for Azure
        trustServerCertificate: true // Use true for local dev
    },
    pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000
    }
};

let pool;

const initializePool = async () => {
    try {
        pool = await sql.connect(dbConfig);
        console.log('Connected to SQL Server');
        return pool;
    } catch (err) {
        console.error('Database connection failed:', err);
        throw err;
    }
};

module.exports = {
    initializePool,
    query: async (text, params) => {
        if (!pool) {
            await initializePool();
        }
        const request = pool.request();
        
        // Add parameters if provided
        if (params) {
            params.forEach((param, index) => {
                request.input(`param${index}`, param);
            });
        }
        
        const result = await request.query(text);
        return { rows: result.recordset };
    },
    sql
};
