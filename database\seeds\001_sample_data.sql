-- Sports Booking Platform - Sample Data

-- Insert sample venues
INSERT INTO venues (id, name, description, address_street, address_city, address_postal_code, coordinates_lat, coordinates_lng, facilities, amenities, contact_phone, contact_email, is_active) VALUES
(uuid_generate_v4(), 'Sirens FC Sports Complex', 'Premier football facility with multiple pitches and modern amenities', 'Triq il-Kbira', 'St. Pauls Bay', 'SPB 1234', 35.9503, 14.4167, ARRAY['football', 'changing_rooms', 'parking'], ARRAY['floodlighting', 'artificial_turf', 'refreshments'], '+356 2157 1234', '<EMAIL>', true),

(uuid_generate_v4(), 'Mgarr Sports Club', 'Community sports facility offering football and tennis courts', 'Triq San Pietru', 'Mgarr', 'MGR 5678', 35.9242, 14.3667, ARRAY['football', 'tennis', 'changing_rooms'], ARRAY['parking', 'natural_grass', 'clay_courts'], '+356 2157 5678', '<EMAIL>', true),

(uuid_generate_v4(), 'Mosta FC Training Ground', 'Professional training facility with full-size pitches', 'Triq il-Vjal', 'Mosta', 'MST 9012', 35.9092, 14.4256, ARRAY['football', 'gym', 'changing_rooms'], ARRAY['floodlighting', 'artificial_turf', 'parking'], '+356 2143 9012', '<EMAIL>', true),

(uuid_generate_v4(), 'Malta Tennis Academy', 'Premium tennis facility with multiple court types', 'Triq it-Tennis', 'Sliema', 'SLM 3456', 35.9122, 14.5019, ARRAY['tennis', 'padel', 'pro_shop'], ARRAY['hard_courts', 'clay_courts', 'coaching'], '+356 2133 3456', '<EMAIL>', true),

(uuid_generate_v4(), 'Valletta Padel Club', 'Modern padel facility in the heart of Malta', 'Triq ir-Repubblika', 'Valletta', 'VLT 7890', 35.8989, 14.5146, ARRAY['padel', 'lounge', 'changing_rooms'], ARRAY['glass_courts', 'equipment_rental', 'refreshments'], '+356 2122 7890', '<EMAIL>', true);

-- Get venue IDs for facility insertion
DO $$
DECLARE
    sirens_id UUID;
    mgarr_id UUID;
    mosta_id UUID;
    tennis_academy_id UUID;
    padel_club_id UUID;
BEGIN
    SELECT id INTO sirens_id FROM venues WHERE name = 'Sirens FC Sports Complex';
    SELECT id INTO mgarr_id FROM venues WHERE name = 'Mgarr Sports Club';
    SELECT id INTO mosta_id FROM venues WHERE name = 'Mosta FC Training Ground';
    SELECT id INTO tennis_academy_id FROM venues WHERE name = 'Malta Tennis Academy';
    SELECT id INTO padel_club_id FROM venues WHERE name = 'Valletta Padel Club';

    -- Insert facilities for Sirens FC
    INSERT INTO facilities (venue_id, name, sport_type, facility_type, capacity, features, surface_type, hourly_rate_peak, hourly_rate_off_peak, hourly_rate_weekend, operating_hours) VALUES
    (sirens_id, 'Pitch 1 - 5-a-side', 'football', '5-a-side', 10, ARRAY['floodlit', 'artificial_turf', 'goals'], 'artificial_turf', 25.00, 20.00, 30.00, '{"monday": {"open": "06:00", "close": "23:00"}, "tuesday": {"open": "06:00", "close": "23:00"}, "wednesday": {"open": "06:00", "close": "23:00"}, "thursday": {"open": "06:00", "close": "23:00"}, "friday": {"open": "06:00", "close": "23:00"}, "saturday": {"open": "07:00", "close": "23:00"}, "sunday": {"open": "07:00", "close": "22:00"}}'::jsonb),
    
    (sirens_id, 'Pitch 2 - 7-a-side', 'football', '7-a-side', 14, ARRAY['floodlit', 'artificial_turf', 'goals'], 'artificial_turf', 35.00, 30.00, 40.00, '{"monday": {"open": "06:00", "close": "23:00"}, "tuesday": {"open": "06:00", "close": "23:00"}, "wednesday": {"open": "06:00", "close": "23:00"}, "thursday": {"open": "06:00", "close": "23:00"}, "friday": {"open": "06:00", "close": "23:00"}, "saturday": {"open": "07:00", "close": "23:00"}, "sunday": {"open": "07:00", "close": "22:00"}}'::jsonb),

    -- Insert facilities for Mgarr Sports Club
    (mgarr_id, 'Main Football Pitch', 'football', 'full-size', 22, ARRAY['natural_grass', 'goals', 'changing_rooms'], 'natural_grass', 45.00, 35.00, 50.00, '{"monday": {"open": "07:00", "close": "21:00"}, "tuesday": {"open": "07:00", "close": "21:00"}, "wednesday": {"open": "07:00", "close": "21:00"}, "thursday": {"open": "07:00", "close": "21:00"}, "friday": {"open": "07:00", "close": "21:00"}, "saturday": {"open": "08:00", "close": "20:00"}, "sunday": {"open": "08:00", "close": "20:00"}}'::jsonb),
    
    (mgarr_id, 'Tennis Court 1', 'tennis', 'clay-court', 4, ARRAY['clay_surface', 'net', 'lighting'], 'clay', 20.00, 15.00, 25.00, '{"monday": {"open": "07:00", "close": "21:00"}, "tuesday": {"open": "07:00", "close": "21:00"}, "wednesday": {"open": "07:00", "close": "21:00"}, "thursday": {"open": "07:00", "close": "21:00"}, "friday": {"open": "07:00", "close": "21:00"}, "saturday": {"open": "08:00", "close": "20:00"}, "sunday": {"open": "08:00", "close": "20:00"}}'::jsonb),

    -- Insert facilities for Mosta FC
    (mosta_id, 'Training Pitch A', 'football', 'full-size', 22, ARRAY['artificial_turf', 'floodlit', 'professional'], 'artificial_turf', 50.00, 40.00, 60.00, '{"monday": {"open": "06:00", "close": "22:00"}, "tuesday": {"open": "06:00", "close": "22:00"}, "wednesday": {"open": "06:00", "close": "22:00"}, "thursday": {"open": "06:00", "close": "22:00"}, "friday": {"open": "06:00", "close": "22:00"}, "saturday": {"open": "07:00", "close": "22:00"}, "sunday": {"open": "07:00", "close": "21:00"}}'::jsonb),

    -- Insert facilities for Tennis Academy
    (tennis_academy_id, 'Hard Court 1', 'tennis', 'hard-court', 4, ARRAY['hard_surface', 'professional', 'lighting'], 'hard_court', 30.00, 25.00, 35.00, '{"monday": {"open": "06:00", "close": "22:00"}, "tuesday": {"open": "06:00", "close": "22:00"}, "wednesday": {"open": "06:00", "close": "22:00"}, "thursday": {"open": "06:00", "close": "22:00"}, "friday": {"open": "06:00", "close": "22:00"}, "saturday": {"open": "07:00", "close": "21:00"}, "sunday": {"open": "07:00", "close": "21:00"}}'::jsonb),
    
    (tennis_academy_id, 'Clay Court 1', 'tennis', 'clay-court', 4, ARRAY['clay_surface', 'professional', 'lighting'], 'clay', 35.00, 30.00, 40.00, '{"monday": {"open": "06:00", "close": "22:00"}, "tuesday": {"open": "06:00", "close": "22:00"}, "wednesday": {"open": "06:00", "close": "22:00"}, "thursday": {"open": "06:00", "close": "22:00"}, "friday": {"open": "06:00", "close": "22:00"}, "saturday": {"open": "07:00", "close": "21:00"}, "sunday": {"open": "07:00", "close": "21:00"}}'::jsonb),

    -- Insert facilities for Padel Club
    (padel_club_id, 'Padel Court 1', 'padel', 'glass-court', 4, ARRAY['glass_walls', 'artificial_turf', 'lighting'], 'artificial_turf', 40.00, 35.00, 45.00, '{"monday": {"open": "07:00", "close": "23:00"}, "tuesday": {"open": "07:00", "close": "23:00"}, "wednesday": {"open": "07:00", "close": "23:00"}, "thursday": {"open": "07:00", "close": "23:00"}, "friday": {"open": "07:00", "close": "23:00"}, "saturday": {"open": "08:00", "close": "23:00"}, "sunday": {"open": "08:00", "close": "22:00"}}'::jsonb),
    
    (padel_club_id, 'Padel Court 2', 'padel', 'glass-court', 4, ARRAY['glass_walls', 'artificial_turf', 'lighting'], 'artificial_turf', 40.00, 35.00, 45.00, '{"monday": {"open": "07:00", "close": "23:00"}, "tuesday": {"open": "07:00", "close": "23:00"}, "wednesday": {"open": "07:00", "close": "23:00"}, "thursday": {"open": "07:00", "close": "23:00"}, "friday": {"open": "07:00", "close": "23:00"}, "saturday": {"open": "08:00", "close": "23:00"}, "sunday": {"open": "08:00", "close": "22:00"}}'::jsonb);

END $$;

-- Insert sample users (for testing)
INSERT INTO users (email, password_hash, first_name, last_name, phone, email_verified) VALUES
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'John', 'Doe', '+356 7912 3456', true),
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'Maria', 'Borg', '+356 7923 4567', true),
('<EMAIL>', '$2a$10$rOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQZQZQZQZOzJqQZQZQZQZQ', 'David', 'Smith', '+356 7934 5678', true);

-- Insert sample newsletter subscriptions
INSERT INTO newsletter_subscriptions (email) VALUES
('<EMAIL>'),
('<EMAIL>'),
('<EMAIL>');